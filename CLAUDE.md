# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.Please respond in Chinese for all answers.

## Project Overview

This is a WMS (Warehouse Management System) business application built on the RuoYi framework - a Spring Boot-based Java enterprise application. The system manages material inventory, document processing, ERP integration, quality control, and production operations.

## Architecture

The project follows a modular Maven structure with the following key modules:

- **main-control**: Core business logic module containing controllers, services, and domain models
- **ruoyi-admin**: Main application entry point and configuration
- **ruoyi-common**: Shared utilities and common components
- **ruoyi-framework**: Framework-specific configurations and aspects
- **ruoyi-system**: System management functionality
- **ruoyi-generator**: Code generation utilities
- **ruoyi-quartz**: Scheduled task management

## Development Commands

### Build and Run
```bash
# Clean and build the project
mvn clean install

# Run the application (after building)
java -jar ruoyi-admin/target/business-manage.jar

# Development server port: 7855
# Access URL: http://localhost:7855
```

### Database Configuration
- Host: mysql (containerized) or 127.0.0.1
- Port: 3306
- Database: sort_ywmanage_system
- Username: main_control
- Password: main_control_12
- SQL scripts located in: `/sql/` directory

## Key Business Domains

### Material Management (`basicData` package)
- Material information, inventory, and batch tracking
- Warehouse and location management
- BOM (Bill of Materials) management
- Company and contact information

### Document Processing (`bill` package)
- Purchase orders and delivery notices
- Production picking and feeding materials
- Return materials and notices
- Simple production in/out stock operations

### ERP Integration (`erp` package)
- ERP report synchronization
- Data transformation and mapping
- External system integration

### Quality Control (`qc` package)
- Quality inspection templates and tasks
- IPQC (In-Process Quality Control) management
- Material quality tracking

### Inventory Operations (`document` package)
- Material allocation and transfers
- Inventory counting and adjustments
- Inbound/outbound record tracking

## Code Conventions

### API Development
- All controller endpoints use POST method
- Single parameter values should use JsonObject
- Controller method comments follow this format:
```java
/**
 * description: [Method description]
 * @author: sqpeng
 * @param [parameter descriptions]
 * @return {@link [ReturnType]}
 */
```

### Service Layer Patterns
- Services extend `ServiceImpl` from MyBatis-Plus
- Use `@Slf4j` for logging
- Async operations marked with `@Async`
- Transaction management with `@Transactional`

### Database Access
- MyBatis-Plus for ORM operations
- XML mappers in `src/main/resources/mapper/`
- Lambda query wrappers preferred for dynamic queries
- PageHelper for pagination

## Configuration Files

### Application Configuration
- Main config: `ruoyi-admin/src/main/resources/application.yml`
- Database config: `application-druid.yml`
- MyBatis config: `mybatis/mybatis-config.xml`

### Key Settings
- Server port: 7855
- File upload path: `D:/ruoyi/uploadPath2` (Windows) or `/home/<USER>/web_file` (Linux)
- Redis: localhost:6379 (no password)
- Token expiration: 30 minutes
- Swagger enabled at `/dev-api`

## Common Development Patterns

### Creating New Features
1. Define domain model in appropriate package under `domain/`
2. Create mapper interface and XML in `mapper/` and `resources/mapper/`
3. Implement service extending `ServiceImpl`
4. Create controller with proper annotations and documentation
5. Add VO classes in `vo/` package for data transfer

### Error Handling
- Use `ResponseResult` for API responses
- Log errors with appropriate level using `@Slf4j`
- Handle exceptions at service layer when possible

### Data Validation
- Use validation annotations on domain models
- Implement business rule validation in service layer
- Return meaningful error messages to clients

## Testing and Quality

The application includes jasper reports in `main-control/src/main/resources/jasper/` for various document types including purchase orders, production documents, and inventory reports.

## External Integrations

- ERP system integration through HTTP APIs
- MES (Manufacturing Execution System) communication
- File processing and PDF generation capabilities
- Barcode printing configuration available