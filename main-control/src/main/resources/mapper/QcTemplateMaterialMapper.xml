<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.qc.QcTemplateMaterialMapper">

    <select id="queryQcTemplateMaterial" resultType="com.ruoyi.vo.qc.QcTemplateMaterialVo">
        select qtm.*,
               bmi.material_name,
               bmi.specifications,
               bmi.texture,
               bmi.purchase_unit
        from qc_template_material qtm
        left join basic_material_info bmi on qtm.material_code = bmi.material_code
        where 1 = 1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND qtm.template_code = #{keyWord}
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND (qtm.material_code LIKE CONCAT('%', #{keySubWord}, '%') OR bmi.material_name LIKE CONCAT('%', #{keySubWord}, '%'))
        </if>
        order by qtm.create_time desc
    </select>
    <select id="getTemplateMaterialByMaterialCodeAndQcType"
            resultType="com.ruoyi.domain.qc.QcTemplateMaterial">
        select qtm.*
        from qc_template_material qtm
        left join qc_template_info qti on qtm.template_code = qti.template_code
        where qtm.material_code = #{materialCode}
          and qti.qc_type = #{qcType} and qti.enable_flag = 1
    </select>

</mapper>

