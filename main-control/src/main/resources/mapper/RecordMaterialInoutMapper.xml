<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.RecordMaterialInoutMapper">

  <update id="updateByPrimaryKey" parameterType="com.ruoyi.domain.basicData.RecordMaterialInout">
    <!--@mbg.generated-->
    update record_material_inout
    <set>
      <if test="bound_index != null and bound_index != ''">
        bound_index = #{bound_index,jdbcType=VARCHAR},
      </if>
      <if test="bound_type != null">
        bound_type = #{bound_type,jdbcType=INTEGER},
      </if>
      <if test="data_origin != null">
        data_origin = #{data_origin,jdbcType=INTEGER},
      </if>
      <if test="inout_type != null">
        inout_type = #{inout_type,jdbcType=INTEGER},
      </if>
      <if test="material_code != null and material_code != ''">
        material_code = #{material_code,jdbcType=VARCHAR},
      </if>
      <if test="container_code != null and container_code != ''">
        container_code = #{container_code,jdbcType=VARCHAR},
      </if>
      <if test="batch != null and batch != ''">
        batch = #{batch,jdbcType=VARCHAR},
      </if>
      <if test="produce_date != null and produce_date != ''">
        produce_date = #{produce_date,jdbcType=VARCHAR},
      </if>
      <if test="upper_index != null and upper_index != ''">
        upper_index = #{upper_index,jdbcType=VARCHAR},
      </if>
      <if test="total_num != null">
        total_num = #{total_num,jdbcType=INTEGER},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="record_date != null">
        record_date = #{record_date,jdbcType=TIMESTAMP},
      </if>
      <if test="recorder != null and recorder != ''">
        recorder = #{recorder,jdbcType=VARCHAR},
      </if>
      <if test="lock_time != null">
        lock_time = #{lock_time,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="queryRecordMaterialInout" resultType="com.ruoyi.vo.report.RecordMaterialInoutVo">
    select
    rmi.*,
    bwi.warehouse_name,
    bmi.material_name,
    bmi.material_img
    from
    record_material_inout rmi
    left join
    basic_warehouse_container bwc on
    rmi.container_code = bwc.container_code
    left join
    basic_material_info bmi on
    bmi.material_code = rmi.material_code
    left join
    basic_warehouse_location bwl on
    bwc.location_code = bwl.location_code
    left join
    basic_warehouse_info bwi on
    bwl.warehouse_code = bwi.warehouse_code
    where
    1 = 1
    <if test = "keyWord != null and keyWord.trim() != ''">
      and (rmi.bound_index like CONCAT('%', #{keyWord}, '%') OR rmi.upper_index LIKE CONCAT('%', #{keyWord}, '%'))
    </if>
    <if test = "keySubWord != null and keySubWord.trim() != ''">
      and (bmi.material_name like CONCAT('%', #{keySubWord}, '%'))
    </if>
    <if test = "keyThirdWord != null and keyThirdWord.trim() != ''">
      and (bwi.warehouse_name like CONCAT('%', #{keyThirdWord}, '%'))
    </if>
    <if test = "state != null">
      and rmi.bound_type = #{state}
    </if>
    <if test = "stateSub != null">
      and rmi.inout_type = #{stateSub}
    </if>
    <if test = "bdate != null and bdate!='' ">
      and DATE_FORMAT(rmi.record_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{bdate},'%Y-%m-%d')
    </if>
    <if test = "edate != null and edate != '' ">
      and DATE_FORMAT(rmi.record_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{edate},'%Y-%m-%d')
    </if>
    order by
    rmi.record_date desc
  </select>
</mapper>