<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicWarehouseContainerMapper">
    <select id="getMaxIndex" resultType="java.lang.String">
        select container_code
        from basic_warehouse_container
        where 1 = 1
          and container_code like #{strDate}
        order by container_code desc limit 1
    </select>
    <select id="queryContainerLocationInfo"
            resultType="com.ruoyi.vo.warehouse.ContainerLocationInfoDto">
        select
        bwc.container_code AS containerCode,
        bwi.warehouse_name AS warehouseName,
        b.node_name as shelfName,
        a.node_name as levelName,
        bwl.node_name as positionName
        from
        basic_warehouse_container bwc
        join
        basic_warehouse_location bwl on
        bwc.location_code = bwl.location_code
        left join
        basic_warehouse_location a on
        a.id = bwl.parent_id
        left join
        basic_warehouse_location b on
        b.id = a.parent_id
        left join
        basic_warehouse_info bwi on
        bwl.warehouse_code = bwi.warehouse_code
        where
        1 = 1
        AND bwc.container_code = #{code} or bwc.id = #{code}
        limit 1
    </select>

    <select id="queryContainerLocationInfoBatch"
            resultType="com.ruoyi.vo.warehouse.ContainerLocationInfoDto">
        select
        bwc.container_code AS containerCode,
        bwi.warehouse_name AS warehouseName,
        b.node_name as shelfName,
        a.node_name as levelName,
        bwl.node_name as positionName
        from
        basic_warehouse_container bwc
        join
        basic_warehouse_location bwl on
        bwc.location_code = bwl.location_code
        left join
        basic_warehouse_location a on
        a.id = bwl.parent_id
        left join
        basic_warehouse_location b on
        b.id = a.parent_id
        left join
        basic_warehouse_info bwi on
        bwl.warehouse_code = bwi.warehouse_code
        where
        1 = 1
        <if test="containerCodes != null and containerCodes.size() > 0">
            AND bwc.container_code IN
            <foreach collection="containerCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="getContainerByCode" resultType="com.ruoyi.domain.basicData.BasicWarehouseContainer">
        select *
        from basic_warehouse_container
        where 1 = 1
          and container_code = #{containerCode}
    </select>
</mapper>