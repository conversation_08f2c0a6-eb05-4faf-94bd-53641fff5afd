<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.DocumentInventoryDetailMapper">
    <select id="queryDocumentInventoryDetail" resultType="com.ruoyi.vo.basicData.DocumentInventoryDetailDto">
        SELECT *
        FROM document_inventory_detail a
        left join basic_document_detail b on a.detail_code = b.id
        left join basic_document_info c on b.document_code = c.id
        WHERE 1 = 1
        <if test="keyWord != null and keyWord != ''">
            AND a.detail_code = #{keyWord}
        </if>
        <if test="keySubWord != null and keySubWord != ''">
            AND c.transaction_code = #{keySubWord}
        </if>
    </select>

    <select id="selectByDetailCode" resultType="com.ruoyi.domain.basicData.DocumentInventoryDetail">
        SELECT *
        FROM document_inventory_detail
        WHERE detail_code = #{detailCode}
        ORDER BY id
    </select>
</mapper>

