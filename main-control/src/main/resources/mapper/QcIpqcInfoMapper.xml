<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.qc.QcIpqcInfoMapper">

    <select id="getMaxIndex" resultType="java.lang.String">
        select ipqc_code from qc_ipqc_task_info where ipqc_code like #{strDate} order by ipqc_code desc limit 1
    </select>
    <select id="queryQcIpqcInfo" resultType="com.ruoyi.vo.qc.QcIpqcInfoVo">
        select qii.*,
        qti.template_name,
        bmi.material_name,
        bmi.specifications,
        bmi.texture,
        bmi.purchase_unit,
        bci.company_name,
        bat.total_check_num,
        bat.total_qualified_num,
        bat.total_unqualified_num
        from qc_ipqc_task_info qii
        left join qc_template_info qti on qii.template_code = qti.template_code
        left join basic_company_info bci on qii.company_code = bci.company_code
        left join basic_material_info bmi on qii.material_code = bmi.material_code
        left join qc_ipqc_batch_task bat on qii.batch_task = bat.batch_task
        where 1 = 1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND (qii.ipqc_code LIKE CONCAT('%', #{keyWord}, '%') OR qii.source_task_no LIKE CONCAT('%', #{keyWord}, '%'))
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND qii.qc_type = #{keySubWord}
        </if>
        <if test="keyThirdWord != null and keyThirdWord.trim() != ''">
            AND qii.status = #{keyThirdWord}
        </if>
        <if test="keyFourWord != null and keyFourWord.trim() != ''">
            AND (bmi.material_code LIKE CONCAT('%', #{keyFourWord}, '%')
            OR bmi.material_name LIKE CONCAT('%', #{keyFourWord}, '%')
            OR qii.batch_task LIKE CONCAT('%', #{keyFourWord}, '%'))
        </if>
        order by qii.create_time desc
    </select>


</mapper>

