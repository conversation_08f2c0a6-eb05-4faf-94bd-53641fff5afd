<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicDocumentInfoMapper">
    <select id="getDocumentInfoByCode" resultType="com.ruoyi.domain.basicData.BasicDocumentInfo">
        select * from basic_document_info where id = #{transactionCode} or transaction_code = #{transactionCode} limit 1
    </select>

    <!-- 查询单据信息（支持物料查询、供应商/客户名称查询等） -->
    <select id="queryDocumentInfoEnhanced" resultType="com.ruoyi.domain.basicData.BasicDocumentInfo">
        SELECT DISTINCT di.*, ci.company_name as supplierCustomerName
        FROM basic_document_info di

        <!-- 总是JOIN公司信息表，用于获取供应商/客户名称 -->
        LEFT JOIN basic_company_info ci ON di.supply_sales_code = ci.company_code

        <!-- 当需要查询物料信息时，JOIN明细表和物料表 -->
        <if test="param.materialInfo != null and param.materialInfo != ''">
            LEFT JOIN basic_document_detail dd ON di.id = dd.document_code
            LEFT JOIN basic_material_info mi ON dd.material_code = mi.material_code
        </if>

        WHERE 1=1

        <if test="param.keyWord != null and param.keyWord != ''">
            AND di.transaction_code LIKE CONCAT('%', #{param.keyWord}, '%')
        </if>
        <if test="param.keySubWord != null and param.keySubWord != ''">
            AND di.business_type = #{param.keySubWord}
        </if>
        <if test="param.keyThirdWord != null and param.keyThirdWord != ''">
            AND di.status = #{param.keyThirdWord}
        </if>
        <if test="param.keyFourWord != null and param.keyFourWord != ''">
            AND di.is_lock = #{param.keyFourWord}
        </if>

        <!-- 供应商/客户编码/名称模糊搜索 -->
        <if test="param.supplierCustomer != null and param.supplierCustomer != ''">
            AND (ci.company_code LIKE CONCAT('%', #{param.supplierCustomer}, '%')
                 OR ci.company_name LIKE CONCAT('%', #{param.supplierCustomer}, '%'))
            <!-- 根据单据类型限制公司类型 -->
            <if test="param.keySubWord != null and param.keySubWord != ''">
                <choose>
                    <!-- 采购相关单据：查询供应商（company_type=1） -->
                    <when test="param.keySubWord == '5' or param.keySubWord == '6'">
                        AND ci.company_type = 1
                    </when>
                    <!-- 销售相关单据：查询客户（company_type=0） -->
                    <when test="param.keySubWord == '7' or param.keySubWord == '8'">
                        AND ci.company_type = 0
                    </when>
                </choose>
            </if>
        </if>

        <!-- 物料信息模糊搜索（同时搜索编码和名称） -->
        <if test="param.materialInfo != null and param.materialInfo != ''">
            AND (mi.material_code LIKE CONCAT('%', #{param.materialInfo}, '%')
                 OR mi.material_name LIKE CONCAT('%', #{param.materialInfo}, '%'))
        </if>

        <!-- 来源单据编号搜索 -->
        <if test="param.sourceOrderNo != null and param.sourceOrderNo != ''">
            AND di.source_document_no LIKE CONCAT('%', #{param.sourceOrderNo}, '%')
        </if>

        <!-- 批次号搜索 -->
        <if test="param.batchNo != null and param.batchNo != ''">
            AND EXISTS (
                SELECT 1 FROM basic_material_batch_inventory bmbi
                WHERE bmbi.upper_index = di.id
                AND bmbi.batch LIKE CONCAT('%', #{param.batchNo}, '%')
            )
        </if>

        <!-- 时间范围查询 -->
        <if test="param.bdate != null and param.bdate != ''">
            AND di.rece_time &gt;= #{param.bdate}
        </if>
        <if test="param.edate != null and param.edate != ''">
            AND di.rece_time &lt;= #{param.edate}
        </if>

        ORDER BY di.rece_time DESC
    </select>
</mapper>

