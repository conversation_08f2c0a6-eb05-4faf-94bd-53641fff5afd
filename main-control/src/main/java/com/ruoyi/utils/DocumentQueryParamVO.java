package com.ruoyi.utils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 出入库单据查询参数
 * 继承通用查询参数，新增出入库业务相关的查询字段
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentQueryParamVO extends QueryParamVO {

    /**
     * 供应商/客户编码或名称（模糊搜索）
     * 同时搜索company_code和company_name字段
     * 根据单据类型动态使用：
     * - 采购相关单据(CGRK, CGTH)：搜索供应商编码/名称（company_type=1）
     * - 销售相关单据(XSCK, XSTH)：搜索客户编码/名称（company_type=0）
     * - 生产相关单据：不使用此字段
     */
    private String supplierCustomer;

    /**
     * 物料信息（模糊搜索）
     * 同时搜索物料编码和物料名称
     */
    private String materialInfo;

    /**
     * 来源单据编号（模糊搜索source_document_no字段）
     * 包括：收料通知单、退料申请单、退货通知单、发货通知单、
     *      生产领料单、生产退料单、生产入库单等ERP来源单据编号
     */
    private String sourceOrderNo;

    /**
     * 批次号
     */
    private String batchNo;

}
