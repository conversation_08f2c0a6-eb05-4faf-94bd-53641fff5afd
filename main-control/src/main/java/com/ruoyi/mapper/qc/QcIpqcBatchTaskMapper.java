package com.ruoyi.mapper.qc;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.qc.QcIpqcBatchTask;
import com.ruoyi.domain.qc.QcIpqcTaskInfo;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.qc.QcIpqcInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 类描述
 */
public interface QcIpqcBatchTaskMapper extends BaseMapper<QcIpqcBatchTask> {

}

