package com.ruoyi.mapper.basicData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicWarehouseContainer;
import com.ruoyi.vo.warehouse.ContainerLocationInfoDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BasicWarehouseContainerMapper extends BaseMapper<BasicWarehouseContainer> {

    String getMaxIndex(@Param("strDate") String var1);

    ContainerLocationInfoDto queryContainerLocationInfo(@Param("code") String code);

    /**
     * 批量查询容器位置信息
     *
     * @param containerCodes 容器编码列表
     * @return 容器位置信息列表
     */
    List<ContainerLocationInfoDto> queryContainerLocationInfoBatch(@Param("containerCodes") List<String> containerCodes);

    BasicWarehouseContainer getContainerByCode(@Param("containerCode")String container_code);
}
