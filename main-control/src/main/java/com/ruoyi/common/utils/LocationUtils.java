package com.ruoyi.common.utils;

import com.ruoyi.vo.warehouse.ContainerLocationInfoDto;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 位置信息处理工具类
 * 
 * <AUTHOR>
 */
public class LocationUtils {

    /**
     * 默认分隔符
     */
    private static final String DEFAULT_SEPARATOR = "-";

    /**
     * 智能拼接位置信息，自动过滤空值和null值
     * 
     * @param locationInfo 位置信息DTO
     * @return 拼接后的位置字符串，如果所有字段都为空则返回空字符串
     */
    public static String formatLocation(ContainerLocationInfoDto locationInfo) {
        return formatLocation(locationInfo, DEFAULT_SEPARATOR);
    }

    /**
     * 智能拼接位置信息，自动过滤空值和null值
     * 
     * @param locationInfo 位置信息DTO
     * @param separator 分隔符
     * @return 拼接后的位置字符串，如果所有字段都为空则返回空字符串
     */
    public static String formatLocation(ContainerLocationInfoDto locationInfo, String separator) {
        if (locationInfo == null) {
            return "";
        }

        List<String> parts = new ArrayList<>();
        
        // 按层级顺序添加非空字段
        if (StringUtils.isNotBlank(locationInfo.getWarehouseName())) {
            parts.add(locationInfo.getWarehouseName().trim());
        }
        if (StringUtils.isNotBlank(locationInfo.getShelfName())) {
            parts.add(locationInfo.getShelfName().trim());
        }
        if (StringUtils.isNotBlank(locationInfo.getLevelName())) {
            parts.add(locationInfo.getLevelName().trim());
        }
        if (StringUtils.isNotBlank(locationInfo.getPositionName())) {
            parts.add(locationInfo.getPositionName().trim());
        }

        return String.join(separator, parts);
    }

    /**
     * 直接通过字段拼接位置信息
     * 
     * @param warehouseName 仓库名称
     * @param shelfName 货架名称
     * @param levelName 层级名称
     * @param positionName 位置名称
     * @return 拼接后的位置字符串
     */
    public static String formatLocation(String warehouseName, String shelfName, 
                                      String levelName, String positionName) {
        return formatLocation(warehouseName, shelfName, levelName, positionName, DEFAULT_SEPARATOR);
    }

    /**
     * 直接通过字段拼接位置信息
     * 
     * @param warehouseName 仓库名称
     * @param shelfName 货架名称
     * @param levelName 层级名称
     * @param positionName 位置名称
     * @param separator 分隔符
     * @return 拼接后的位置字符串
     */
    public static String formatLocation(String warehouseName, String shelfName, 
                                      String levelName, String positionName, String separator) {
        List<String> parts = new ArrayList<>();
        
        if (StringUtils.isNotBlank(warehouseName)) {
            parts.add(warehouseName.trim());
        }
        if (StringUtils.isNotBlank(shelfName)) {
            parts.add(shelfName.trim());
        }
        if (StringUtils.isNotBlank(levelName)) {
            parts.add(levelName.trim());
        }
        if (StringUtils.isNotBlank(positionName)) {
            parts.add(positionName.trim());
        }

        return String.join(separator, parts);
    }
}
