package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.domain.basicData.BasicMaterialClassify;
import com.ruoyi.domain.basicData.BasicMaterialInfo;
import com.ruoyi.mapper.basicData.MaterialInfoMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import com.ruoyi.vo.erp.dto.ErpMaterialInfoVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webResponse.dto.BasicMaterialInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: psy
 * @CreateDate: 2025/3/17 11:49
 * @Description: 物料信息
 */
@Service
@Slf4j
public class BasicMaterialInfoService extends ServiceImpl<MaterialInfoMapper, BasicMaterialInfo> {

    @Resource
    private MaterialInfoMapper materialInfoMapper;

    @Resource
    private BasicBomVersionService bomVersionService;

    @Resource
    private BasicMaterialClassifyService materialClassifyService;

    @Resource
    private ErpService erpService;

    @Resource
    private MaterialClassifyTypeMappingService materialClassifyTypeMappingService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 从ERP同步物料数据
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        // 1. 构建ERP查询请求
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.MATERIAL);
        queryReq.setFieldKeys(String.join(",",
                CommonConstant.ErpFieldKeys.Material.ID,
                CommonConstant.ErpFieldKeys.Material.NAME,
                CommonConstant.ErpFieldKeys.Material.CODE,
                CommonConstant.ErpFieldKeys.Material.GROUP,
                CommonConstant.ErpFieldKeys.Material.BASE_PROPERTY,
                CommonConstant.ErpFieldKeys.Material.SPECIFICATION,
                CommonConstant.ErpFieldKeys.Material.BASE_UNIT,
                CommonConstant.ErpFieldKeys.Material.PURCHASE_UNIT,
                CommonConstant.ErpFieldKeys.Material.PRODUCE_UNIT,
                CommonConstant.ErpFieldKeys.Material.ERP_CLS_ID,
                CommonConstant.ErpFieldKeys.Material.IS_SUB_CONTRACT,
                CommonConstant.ErpFieldKeys.Material.IS_PRODUCE,
                CommonConstant.ErpFieldKeys.Material.IS_PURCHASE,
                CommonConstant.ErpFieldKeys.Material.FLENGTH,
                CommonConstant.ErpFieldKeys.Material.FWIDTH,
                CommonConstant.ErpFieldKeys.Material.FHEIGHT));
        queryReq.setFilterString("");
        queryReq.setOrderString("");
        queryReq.setTopRowCount(0);
        queryReq.setStartRow(0);
        queryReq.setLimit(0);

        // 2. 从ERP获取数据
        List<Map<String, Object>> erpDataList = erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_MATERIAL);

        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("从ERP未查询到新的物料信息。");
            return ResponseResult.getSuccessResult("未查询到物料信息", 0);
        }
        log.info("从ERP查询到 {} 条物料信息，准备同步到本地数据库。", erpDataList.size());

        // 在所有操作开始前，对源数据按物料编码进行去重，保留后者
        Map<String, Map<String, Object>> uniqueMaterialMap = erpDataList.stream()
                .collect(Collectors.toMap(
                        map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.CODE)),
                        map -> map,
                        (oldValue, newValue) -> newValue,
                        LinkedHashMap::new
                ));
        List<Map<String, Object>> uniqueErpMaterials = new ArrayList<>(uniqueMaterialMap.values());
        log.info("开始批量同步物料数据，源数据: {} 条, 去重后: {} 条", erpDataList.size(), uniqueErpMaterials.size());

        // 1. 将所有传入数据转换为 ErpMaterialInfoVo 对象列表
        List<ErpMaterialInfoVo> erpMaterialVos = uniqueErpMaterials.stream()
                .map(map -> objectMapper.convertValue(map, ErpMaterialInfoVo.class))
                .collect(Collectors.toList());

        // 2. 提取所有物料编码，一次性查询数据库中已存在的记录
        List<String> materialCodes = erpMaterialVos.stream()
                .map(ErpMaterialInfoVo::getFNumber)
                .collect(Collectors.toList());

        LambdaQueryWrapper<BasicMaterialInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BasicMaterialInfo::getMaterialCode, materialCodes);
        Map<String, BasicMaterialInfo> existingMaterialMap = this.list(queryWrapper).stream()
                .collect(Collectors.toMap(BasicMaterialInfo::getMaterialCode, material -> material));

        log.info("批量查询DB完成(按物料编码)，查询到 {} 条已存在记录", existingMaterialMap.size());

        // 2. 批量获取物料分类信息
        Set<String> classifyIds = erpMaterialVos.stream()
                .map(ErpMaterialInfoVo::getFMaterialGroup)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        Map<String, BasicMaterialClassify> classifyMap = new HashMap<>();
        if (!classifyIds.isEmpty()) {
            classifyMap.putAll(materialClassifyService.list(new LambdaQueryWrapper<BasicMaterialClassify>()
                            .in(BasicMaterialClassify::getId, classifyIds)).stream()
                    .collect(Collectors.toMap(BasicMaterialClassify::getId, Function.identity(), (v1, v2) -> v1)));
        }
        log.info("根据 {} 个物料分组ID，查询到 {} 个物料分类实体", classifyIds.size(), classifyMap.size());

        // 3. 获取物料类型映射关系（用于判断是否发货件）
        Map<String, String> materialTypeMapping = materialClassifyTypeMappingService.getMaterialTypeMapping();
        log.info("获取物料类型映射配置，共 {} 条规则", materialTypeMapping.size());

        // 去重，使用物料编码作为唯一标识
        List<ErpMaterialInfoVo> distinctErpMaterialList = erpMaterialVos.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                ErpMaterialInfoVo::getFNumber,
                                Function.identity(),
                                (oldValue, newValue) -> oldValue
                        ),
                        map -> new ArrayList<>(map.values())
                ));
        log.info("去重后，剩余 {} 条数据", distinctErpMaterialList.size());

        List<BasicMaterialInfo> toAddList = new ArrayList<>();
        List<BasicMaterialInfo> toUpdateList = new ArrayList<>();

        // 5. 对比数据，准备新增和更新列表
        distinctErpMaterialList.forEach(erpVo -> {
            BasicMaterialInfo existingMaterial = existingMaterialMap.get(erpVo.getFNumber());
            // 判断物料类型 - 使用物料分类的type字段
            int materialType = 0;
            BasicMaterialClassify classify = classifyMap.get(erpVo.getFMaterialGroup());
            if (classify != null && classify.getType() != null) {
                try {
                    materialType = Integer.parseInt(classify.getType());
                    log.debug("物料 {} 根据分类 {} 设置类型为: {}", erpVo.getFNumber(), classify.getClassifyName(), materialType);
                } catch (NumberFormatException e) {
                    log.warn("物料分类 {} 的type字段 '{}' 无法转换为整数，使用默认类型0", classify.getClassifyName(), classify.getType());
                    materialType = 0;
                }
            } else if (classify != null) {
                log.warn("物料分类 {} 的type字段为空，使用默认类型0", classify.getClassifyName());
            }

            // 判断是否发货件 - 根据物料类型映射，类型8代表发货件
            int isShippedItem = determineShippedItem(classify, materialTypeMapping, erpVo.getFNumber());
            // 判断是否外购 - 根据ERP字段组合判断
            int isOutSource = determineOutSource(erpVo);
            // 判断是否外加工 - 根据FIsSubContract字段判断
            int isOutProcess = determineOutProcess(erpVo);
            if (existingMaterial != null) {
                // 存在，准备更新
                mapVoToEntity(existingMaterial, erpVo); // 通用字段映射
                if (classify != null) {
                    existingMaterial.setClassifyCode(classify.getClassifyCode());
                }
                existingMaterial.setUpdateTime(new Date());
                existingMaterial.setUpdateName("erp");
                existingMaterial.setMaterialSort(materialType);
                existingMaterial.setIsShippedItem(isShippedItem);
                existingMaterial.setIsOutSource(isOutSource);
                existingMaterial.setIsOutProcess(isOutProcess);
                toUpdateList.add(existingMaterial);
            } else {
                // 不存在，准备新增
                BasicMaterialInfo newMaterial = new BasicMaterialInfo();
                mapVoToEntity(newMaterial, erpVo); // 通用字段映射
                if (classify != null) {
                    newMaterial.setClassifyCode(classify.getClassifyCode());
                }
                newMaterial.setId(erpVo.getFMaterialId());
                newMaterial.setCreateTime(new Date());
                newMaterial.setCreateName("erp");
                newMaterial.setMaterialSort(materialType);
                newMaterial.setIsShippedItem(isShippedItem);
                newMaterial.setIsOutSource(isOutSource);
                newMaterial.setIsOutProcess(isOutProcess);
                toAddList.add(newMaterial);
            }
        });

        // 6. 批量执行数据库操作
        if (!toAddList.isEmpty()) {
            this.saveBatch(toAddList);
            log.info("成功新增 {} 条物料信息。", toAddList.size());
        }
        if (!toUpdateList.isEmpty()) {
            this.updateBatchById(toUpdateList);
            log.info("成功更新 {} 条物料信息。", toUpdateList.size());
        }

        log.info("物料同步完成。");
        return ResponseResult.getSuccessResult(String.format("同步物料信息完成，新增 %d 条，更新 %d 条。", toAddList.size(), toUpdateList.size()), "");
    }

    /**
     * 通用的VO到实体映射方法
     *
     * @param material 目标实体
     * @param erpVo    源VO
     */
    private void mapVoToEntity(BasicMaterialInfo material, ErpMaterialInfoVo erpVo) {
        material.setMaterialName(erpVo.getFName());
        material.setMaterialCode(erpVo.getFNumber());
        material.setSpecifications(erpVo.getFSpecification());
        material.setPurchaseUnit(erpVo.getFPurchaseUnitId());
        material.setProduceUnit(erpVo.getFProduceUnitId());
        material.setMaterialLength(erpVo.getFLength());
        material.setMaterialWidth(erpVo.getFWidth());
        material.setMaterialThickness(erpVo.getFHeight());
    }

    /**
     * 新增物料信息
     */
    public ResponseResult insertBasicMaterialInfo(BasicMaterialInfo basicMaterialInfo) {
        basicMaterialInfo.setId(LocalStringUtils.getDataUUID());
        basicMaterialInfo.setCreateTime(new Date());
        int result = this.materialInfoMapper.insert(basicMaterialInfo);
        if (result >= 1) {
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    /**
     * 删除物料信息
     */
    public ResponseResult deleteBasicMaterialInfo(BatchIdsReq req) {
        int count = this.materialInfoMapper.deleteBatchIds(req.getIds());
        if (count >= req.getIds().size()) {
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    /**
     * 更新物料信息
     */
    public ResponseResult uptBasicMaterialInfo(BasicMaterialInfo basicMaterialInfo) {
        String versionId = basicMaterialInfo.getVersionId();
//        if(this.bomVersionService.getById(versionId) != null){
        basicMaterialInfo.setUpdateTime(new Date());
//        basicMaterialInfo.setUpdateName("admin");
        basicMaterialInfo.setUpdateName(SecurityUtils.getUsername());
        int count = this.materialInfoMapper.updateById(basicMaterialInfo);
        if (count >= 1) {
            return ResponseResult.getSuccessResult();
        }
//        }
        return ResponseResult.getErrorResult("版本不存在，数据更新失败，请重新操作");
    }

    /**
     * 分页查询
     */
    public List<BasicMaterialInfoDto> queryBasicMaterialInfo(QueryParamVO queryParamVO) {
        return materialInfoMapper.queryBasicMaterialInfo(queryParamVO);
    }

    public ResponseResult lockBasicMaterialInfo(BatchIdsReq batchIdsReq) {
        this.materialInfoMapper.lockBasicMaterialInfo(batchIdsReq.getIds());
        return ResponseResult.getSuccessResult();
    }

    public BasicMaterialInfo queryBasicMaterialByCode(String keyWord) {
        LambdaQueryWrapper<BasicMaterialInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicMaterialInfo::getMaterialCode, keyWord);
        return materialInfoMapper.selectOne(wrapper);
    }

    /**
     * 判断是否外购
     * 根据ERP字段组合判断：、FIsProduce、FIsPurchase
     *
     * @param erpVo ERP物料信息
     * @return 0自产 1外购 2均可
     */
    private int determineOutSource(ErpMaterialInfoVo erpVo) {
        boolean isProduce = "true".equalsIgnoreCase(erpVo.getFIsProduce());
        boolean isPurchase = "true".equalsIgnoreCase(erpVo.getFIsPurchase());
        log.debug("物料 {} ERP字段值 -  允许生产:{}, 允许采购:{}",
                erpVo.getFNumber(),  erpVo.getFIsProduce(), erpVo.getFIsPurchase());

        // 根据业务规则判断
        if (isPurchase && !isProduce) {
            // 只允许采购，不允许生产 -> 外购
            log.debug("物料 {} 设置为外购（只允许采购）", erpVo.getFNumber());
            return CommonConstant.IsOutSource.PURCHASED;
        } else if (!isPurchase && isProduce) {
            // 只允许生产，不允许采购 -> 自产
            log.debug("物料 {} 设置为自产（只允许生产）", erpVo.getFNumber());
            return CommonConstant.IsOutSource.SELF_PRODUCED;
        } else if (isPurchase && isProduce) {
            // 既允许采购又允许生产 -> 均可
            log.debug("物料 {} 设置为均可（既允许采购又允许生产）", erpVo.getFNumber());
            return CommonConstant.IsOutSource.EITHER;
        } else {
            // 都不允许或其他情况 -> 默认自产
            log.debug("物料 {} 设置为自产（默认值）", erpVo.getFNumber());
            return CommonConstant.IsOutSource.SELF_PRODUCED;
        }
    }

    /**
     * 判断是否外加工
     * 根据ERP的FIsSubContract字段判断
     *
     * @param erpVo ERP物料信息
     * @return 0否 1是
     */
    private int determineOutProcess(ErpMaterialInfoVo erpVo) {
        boolean isSubContract = "true".equalsIgnoreCase(erpVo.getFIsSubContract());
        int result = isSubContract ? 1 : 0;
        log.debug("物料 {} 根据委外字段 '{}' 设置外加工为: {}",
                erpVo.getFNumber(), erpVo.getFIsSubContract(), result);
        return result;
    }

    /**
     * 判断是否发货件
     * 优先使用物料分类的type字段，如果无效则通过物料分类名称在映射表中查找
     * @param classify 物料分类信息
     * @param materialTypeMapping 物料类型映射表
     * @param materialCode 物料编码（用于日志）
     * @return 0否 1是
     */
    private int determineShippedItem(BasicMaterialClassify classify, Map<String, String> materialTypeMapping, String materialCode) {
        int typeValue = 0; // 默认类型值
        String source = "默认值"; // 记录类型值来源

        // 优先使用物料分类的type字段
        if (classify != null && classify.getType() != null && !classify.getType().trim().isEmpty()) {
            try {
                typeValue = Integer.parseInt(classify.getType().trim());
                source = "物料分类type字段";
                log.debug("物料 {} 从{}获取类型值: {}", materialCode, source, typeValue);
            } catch (NumberFormatException e) {
                log.warn("物料分类 {} 的type字段 '{}' 无法转换为整数，尝试使用映射表",
                        classify.getClassifyName(), classify.getType());
            }
        }

        // 如果type字段无效，通过物料分类名称在映射表中查找
        if (typeValue == 0 && classify != null && classify.getClassifyName() != null && !materialTypeMapping.isEmpty()) {
            String mappedTypeValue = materialTypeMapping.get(classify.getClassifyName().trim());
            if (mappedTypeValue != null && !mappedTypeValue.trim().isEmpty()) {
                try {
                    typeValue = Integer.parseInt(mappedTypeValue.trim());
                    source = "物料类型映射表";
                    log.debug("物料 {} 从{}获取类型值: {} (分类名称: {})",
                            materialCode, source, typeValue, classify.getClassifyName());
                } catch (NumberFormatException e) {
                    log.warn("物料分类 '{}' 在映射表中的类型值 '{}' 无法转换为整数",
                            classify.getClassifyName(), mappedTypeValue);
                }
            } else {
                log.debug("物料 {} 的分类名称 '{}' 在映射表中未找到对应配置",
                        materialCode, classify != null ? classify.getClassifyName() : "null");
            }
        }

        // 判断是否为发货件（类型值为8）
        int isShippedItem = (typeValue == 8) ? 1 : 0;
        if (isShippedItem == 1) {
            log.debug("物料 {} 根据类型值 {} ({}) 设置为发货件", materialCode, typeValue, source);
        } else {
            log.debug("物料 {} 根据类型值 {} ({}) 设置为非发货件", materialCode, typeValue, source);
        }

        return isShippedItem;
    }
}
