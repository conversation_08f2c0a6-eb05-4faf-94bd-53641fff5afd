package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.domain.basicData.BasicBomInfo;
import com.ruoyi.domain.basicData.BasicProductInfo;
import com.ruoyi.mapper.basicData.ProductInfoMapper;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.basicData.BomInfoAddReq;
import com.ruoyi.vo.basicData.BomInfoDto;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webResponse.dto.BasicProductInfoDto;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/3/17 11:49
 * @Description: 产品信息
 */
@Service
public class BasicProductInfoService extends ServiceImpl<ProductInfoMapper, BasicProductInfo> {

    @Resource
    private ProductInfoMapper productInfoMapper;

    @Resource
    private BasicBomVersionService basicBomVersionService;

    /**
     * 新增产品信息
     */
    public ResponseResult insertBasicProductInfo(BasicProductInfo basicProductInfo) {
        basicProductInfo.setId(LocalStringUtils.getDataUUID());
        basicProductInfo.setCreateTime(new Date());
        int result = this.productInfoMapper.insert(basicProductInfo);
        if (result >= 1) {
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    /**
     * 删除产品信息
     */
    public ResponseResult deleteBasicProductInfo(BatchIdsReq req) {
        int count = this.productInfoMapper.deleteBatchIds(req.getIds());
        if (count >= req.getIds().size()) {
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    /**
     * 更新产品信息
     */
    public ResponseResult uptBasicProductInfo(BasicProductInfo basicProductInfo) {
        /*String versionId = basicProductInfo.getVersionId();
        if(this.basicBomVersionService.getById(versionId) != null){
            basicProductInfo.setUpdateTime(DateAndTimeUtil.getNowDate());
            int count = this.productInfoMapper.updateById(basicProductInfo);
            if(count >= 1){
                return ResponseResult.getSuccessResult();
            }
        }*/
        basicProductInfo.setUpdateTime(DateAndTimeUtil.getNowDate());
        basicProductInfo.setUpdateName(SecurityUtils.getUsername());
        int count = this.productInfoMapper.updateById(basicProductInfo);
        if (count >= 1) {
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页查询
     */
    public List<BasicProductInfoDto> queryBasicProductInfo(QueryParamVO queryParamVO) {
        return productInfoMapper.queryBasicProductInfo(queryParamVO);
    }

    public List<String> getProductCodeList() {
        return productInfoMapper.getProductCodeList();
    }
}
