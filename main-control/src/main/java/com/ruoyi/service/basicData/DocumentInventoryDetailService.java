package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.basicData.DocumentInventoryDetail;
import com.ruoyi.mapper.basicData.BasicWarehouseContainerMapper;
import com.ruoyi.mapper.basicData.DocumentInventoryDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.basicData.DocumentInventoryDetailDto;
import com.ruoyi.vo.warehouse.ContainerLocationInfoDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

@Service
public class DocumentInventoryDetailService extends ServiceImpl<DocumentInventoryDetailMapper, DocumentInventoryDetail> {

    @Resource
    private DocumentInventoryDetailMapper documentInventoryDetailMapper;

    @Resource
    BasicWarehouseContainerMapper basicWarehouseContainerMapper;

    public List<DocumentInventoryDetailDto> queryDocumentInventoryDetail(QueryParamVO queryParamVO) {
        List<DocumentInventoryDetailDto> documentInventoryDetails = documentInventoryDetailMapper.queryDocumentInventoryDetail(queryParamVO);
        for (DocumentInventoryDetailDto documentInventoryDetailDto : documentInventoryDetails){
            String containerCode = documentInventoryDetailDto.getContainerCode();
            if (StringUtils.isNotEmpty(containerCode)) {
                //通过容器编码查询容器位置信息
                ContainerLocationInfoDto containerLocationInfoDto = basicWarehouseContainerMapper.queryContainerLocationInfo(containerCode);
                if (containerLocationInfoDto != null){
                    documentInventoryDetailDto.setLocation(containerLocationInfoDto.getWarehouseName()+ "-" + containerLocationInfoDto.getShelfName() +
                            "-"+containerLocationInfoDto.getLevelName()+ "-"+containerLocationInfoDto.getPositionName());
                }
            }
        }
        return documentInventoryDetails;
    }

    public void saveObjData(DocumentInventoryDetail documentInventoryDetail) {
        documentInventoryDetail.setId(UUID.randomUUID().toString());
        this.documentInventoryDetailMapper.insert(documentInventoryDetail);
    }
}
