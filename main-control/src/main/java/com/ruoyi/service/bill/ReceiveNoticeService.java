package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.ReceiveNotice;
import com.ruoyi.domain.bill.ReceiveNoticeDetail;
import com.ruoyi.mapper.bill.receive.ReceiveNoticeDetailMapper;
import com.ruoyi.mapper.bill.receive.ReceiveNoticeMapper;
import com.ruoyi.service.basicData.BasicDocumentInfoService;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.ReceiveNoticeDetailVo;
import com.ruoyi.vo.bill.ReceiveNoticeVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收料通知单服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReceiveNoticeService extends ServiceImpl<ReceiveNoticeMapper, ReceiveNotice> {

    private static final Logger logger = LoggerFactory.getLogger(ReceiveNoticeService.class);

    @Resource
    private ErpService erpService;

    @Resource
    private ReceiveNoticeMapper receiveNoticeMapper;

    @Resource
    private ReceiveNoticeDetailMapper receiveNoticeDetailMapper;

    @Resource
    private ReceiveNoticeDetailService receiveNoticeDetailService;

    @Resource
    private BasicDocumentInfoService basicDocumentInfoService;

    /**
     * 查询收料通知单列表
     */
    public List<ReceiveNoticeVo> queryReceiveNoticeList(QueryParamVO queryParamVO) {
        return receiveNoticeMapper.queryReceiveNotice(queryParamVO);
    }

    /**
     * 查询收料通知单明细列表
     */
    public List<ReceiveNoticeDetailVo> queryReceiveNoticeDetail(QueryParamVO queryParamVO) {
        return receiveNoticeDetailService.queryReceiveNoticeDetailList(queryParamVO);
    }

    /**
     * 从ERP同步收料通知单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP收料通知单...");

        // 1. 从ERP拉取所有收料通知单数据
        List<Map<String, Object>> erpDataList = fetchAllErpReceiveNoticeData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到收料通知单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到收料通知单", null);
        }
        log.info("从ERP查询到 {} 条收料通知单行记录", erpDataList.size());

        // 2. 按单据ID进行分组
        Map<String, List<Map<String, Object>>> erpNoticesGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.ID))));
        List<String> erpNoticeIds = new ArrayList<>(erpNoticesGrouped.keySet());

        // 3. 查询现有的收料通知单
        Map<String, ReceiveNotice> existingNoticesMap = new HashMap<>();
        if (!erpNoticeIds.isEmpty()) {
            List<ReceiveNotice> existingNotices = this.lambdaQuery()
                    .in(ReceiveNotice::getId, erpNoticeIds)
                    .list();
            existingNoticesMap = existingNotices.stream()
                    .collect(Collectors.toMap(ReceiveNotice::getId, notice -> notice));
        }

        // 4. 准备数据集合
        List<ReceiveNotice> toInsertNotices = new ArrayList<>();
        List<ReceiveNotice> toUpdateNotices = new ArrayList<>();
        List<ReceiveNoticeDetail> toInsertDetails = new ArrayList<>();

        // 5. 处理每个收料通知单
        for (String noticeId : erpNoticeIds) {
            List<Map<String, Object>> noticeRows = erpNoticesGrouped.get(noticeId);
            if (noticeRows.isEmpty()) continue;

            // 使用第一行数据构建主表信息
            Map<String, Object> firstRow = noticeRows.get(0);
            ReceiveNotice notice = mapToReceiveNotice(firstRow);

            if (existingNoticesMap.containsKey(noticeId)) {
                toUpdateNotices.add(notice);
            } else {
                toInsertNotices.add(notice);
            }

            // 构建明细信息
            for (Map<String, Object> row : noticeRows) {
                ReceiveNoticeDetail detail = mapToReceiveNoticeDetail(row, noticeId);
                toInsertDetails.add(detail);
            }
        }

        // 6. 执行数据库操作
        int insertedNotices = 0, updatedNotices = 0, insertedDetails = 0;

        if (!toInsertNotices.isEmpty()) {
            this.saveBatch(toInsertNotices);
            insertedNotices = toInsertNotices.size();
            log.info("新增收料通知单 {} 条", insertedNotices);
        }

        if (!toUpdateNotices.isEmpty()) {
            this.updateBatchById(toUpdateNotices);
            updatedNotices = toUpdateNotices.size();
            log.info("更新收料通知单 {} 条", updatedNotices);
        }

        // 删除现有明细，重新插入（简化处理）
        if (!erpNoticeIds.isEmpty()) {
            LambdaQueryWrapper<ReceiveNoticeDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(ReceiveNoticeDetail::getNoticeId, erpNoticeIds);
            receiveNoticeDetailService.remove(deleteWrapper);
        }

        if (!toInsertDetails.isEmpty()) {
            receiveNoticeDetailService.saveBatch(toInsertDetails);
            insertedDetails = toInsertDetails.size();
            log.info("新增收料通知单明细 {} 条", insertedDetails);
        }

        // 7. 生成采购入库单
        int generatedPurchaseInStocks = 0;
        for (ReceiveNotice notice : toInsertNotices) {
            try {
                basicDocumentInfoService.generatePurchaseInStockFromReceiveNotice(notice);
                generatedPurchaseInStocks++;
            } catch (Exception e) {
                log.error("为收料通知单 {} 生成采购入库单失败: {}", notice.getBillNo(), e.getMessage());
            }
        }

        String summary = String.format("同步完成。收料通知单[新增:%d, 更新:%d], 明细[新增:%d], 生成采购入库单[%d]",
                insertedNotices, updatedNotices, insertedDetails, generatedPurchaseInStocks);
        log.info(summary);

        return ResponseResult.getSuccessResult(summary, "");
    }

    /**
     * 根据单据编号查询收料通知单
     */
    public ReceiveNotice getByBillNo(String billNo) {
        try {
            return this.lambdaQuery()
                    .eq(ReceiveNotice::getBillNo, billNo)
                    .one();
        } catch (Exception e) {
            log.warn("根据单据编号查询收料通知单失败：{}，错误：{}", billNo, e.getMessage());
            return null;
        }
    }

    /**
     * 从ERP获取收料通知单数据
     */
    private List<Map<String, Object>> fetchAllErpReceiveNoticeData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.RECEIVE_NOTICE);
        queryReq.setFieldKeys(String.join(",",
                // Head
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.STOCK_ORG_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.DATE,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.BILL_TYPE_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.OWNER_TYPE_ID_HEAD,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.OWNER_ID_HEAD,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.SUPPLIER_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.RECEIVE_DEPT_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.PUR_ORG_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.PUR_DEPT_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.PURCHASER_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.NOTE,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Head.ACC_TYPE,
                // Entry
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.MATERIAL_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.MATERIAL_NAME,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.MATERIAL_MODEL,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.UNIT_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.ACT_RECEIVE_QTY,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.PRE_DELIVERY_DATE,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.SUP_DEL_QTY,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.PRICE_UNIT_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.PRICE_UNIT_QTY,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.STOCK_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.STOCK_LOC_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.STOCK_UNIT_ID,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.STOCK_QTY,
                CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.PO_QTY
        ));
        queryReq.setLimit(0);
        return erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_RECEIVE_NOTICE);
    }

    /**
     * 将ERP数据映射为收料通知单实体
     */
    private ReceiveNotice mapToReceiveNotice(Map<String, Object> erpData) {
        ReceiveNotice notice = new ReceiveNotice();

        notice.setId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.ID)));
        notice.setBillNo(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.BILL_NO)));
        notice.setStockOrgId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.STOCK_ORG_ID)));

        // 处理日期字段
        Object dateObj = erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.DATE);
        if (dateObj != null) {
            notice.setReceiveDate(new Date(Long.parseLong(dateObj.toString())));
        }

        notice.setBillTypeId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.BILL_TYPE_ID)));
        notice.setOwnerTypeIdHead(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.OWNER_TYPE_ID_HEAD)));
        notice.setOwnerIdHead(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.OWNER_ID_HEAD)));
        notice.setSupplierId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.SUPPLIER_ID)));
        notice.setReceiveDeptId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.RECEIVE_DEPT_ID)));
        notice.setPurOrgId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.PUR_ORG_ID)));
        notice.setPurDeptId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.PUR_DEPT_ID)));
        notice.setPurchaserId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.PURCHASER_ID)));
        notice.setNote(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.NOTE)));
        notice.setAccType(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Head.ACC_TYPE)));

        // 设置审计字段
        notice.setCreateTime(new Date());
        notice.setCreateName("ERP同步");
        notice.setUpdateTime(new Date());
        notice.setUpdateName("ERP同步");

        return notice;
    }
    /**
     * 将ERP数据映射为收料通知单明细实体
     */
    private ReceiveNoticeDetail mapToReceiveNoticeDetail(Map<String, Object> erpData, String noticeId) {
        ReceiveNoticeDetail detail = new ReceiveNoticeDetail();

        detail.setId(UUID.randomUUID().toString());
        detail.setNoticeId(noticeId);
        detail.setMaterialId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.MATERIAL_ID)));
        detail.setMaterialName(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.MATERIAL_NAME)));
        detail.setMaterialModel(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.MATERIAL_MODEL)));
        detail.setUnitId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.UNIT_ID)));

        // 处理数量字段
        Object actReceiveQtyObj = erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.ACT_RECEIVE_QTY);
        if (actReceiveQtyObj != null) {
            detail.setActReceiveQty(new BigDecimal(actReceiveQtyObj.toString()));
        }

        // 处理预计到货日期
        Object preDateObj = erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.PRE_DELIVERY_DATE);
        if (preDateObj != null) {
            detail.setPreDeliveryDate(new Date(Long.parseLong(preDateObj.toString())));
        }

        Object supDelQtyObj = erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.SUP_DEL_QTY);
        if (supDelQtyObj != null) {
            detail.setSupDelQty(new BigDecimal(supDelQtyObj.toString()));
        }

        detail.setPriceUnitId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.PRICE_UNIT_ID)));

        Object priceUnitQtyObj = erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.PRICE_UNIT_QTY);
        if (priceUnitQtyObj != null) {
            detail.setPriceUnitQty(new BigDecimal(priceUnitQtyObj.toString()));
        }

        detail.setStockId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.STOCK_ID)));
        detail.setStockLocId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.STOCK_LOC_ID)));
        detail.setStockUnitId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.STOCK_UNIT_ID)));

        Object stockQtyObj = erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.STOCK_QTY);
        if (stockQtyObj != null) {
            detail.setStockQty(new BigDecimal(stockQtyObj.toString()));
        }

        Object poQtyObj = erpData.get(CommonConstant.ErpFieldKeys.ReceiveNotice.Entry.PO_QTY);
        if (poQtyObj != null) {
            detail.setPoQty(new BigDecimal(poQtyObj.toString()));
        }

        // 设置审计字段
        detail.setCreateTime(new Date());
        detail.setCreateName("ERP同步");
        detail.setUpdateTime(new Date());
        detail.setUpdateName("ERP同步");

        return detail;
    }}