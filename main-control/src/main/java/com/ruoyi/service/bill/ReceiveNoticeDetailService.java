package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.ReceiveNoticeDetail;
import com.ruoyi.mapper.bill.receive.ReceiveNoticeDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReceiveNoticeDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 收料通知单明细服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReceiveNoticeDetailService extends ServiceImpl<ReceiveNoticeDetailMapper, ReceiveNoticeDetail> {

    @Resource
    private ReceiveNoticeDetailMapper receiveNoticeDetailMapper;

    /**
     * 查询收料通知单明细列表
     */
    public List<ReceiveNoticeDetailVo> queryReceiveNoticeDetailList(QueryParamVO queryParamVO) {
        return receiveNoticeDetailMapper.queryReceiveNoticeDetailVo(queryParamVO);
    }

    /**
     * 根据收料通知单ID查询明细列表（使用LambdaQueryWrapper）
     */
    public List<ReceiveNoticeDetail> getDetailsByNoticeId(String noticeId) {
        return this.lambdaQuery()
                .eq(ReceiveNoticeDetail::getNoticeId, noticeId)
                .list();
    }

    /**
     * 根据收料通知单ID和物料ID查询明细
     */
    public ReceiveNoticeDetail getByNoticeIdAndMaterialId(String noticeId, String materialId) {
        try {
            return this.lambdaQuery()
                    .eq(ReceiveNoticeDetail::getNoticeId, noticeId)
                    .eq(ReceiveNoticeDetail::getMaterialId, materialId)
                    .one();
        } catch (Exception e) {
            log.warn("根据收料通知单ID和物料ID查询明细失败，收料通知单ID：{}，物料ID：{}，错误：{}", noticeId, materialId, e.getMessage());
            return null;
        }
    }
}
