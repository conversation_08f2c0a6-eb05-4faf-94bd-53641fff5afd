package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.ReturnNoticeDetail;
import com.ruoyi.mapper.bill.ReturnNoticeDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReturnNoticeDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退货通知单明细服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReturnNoticeDetailService extends ServiceImpl<ReturnNoticeDetailMapper, ReturnNoticeDetail> {

    @Resource
    private ReturnNoticeDetailMapper returnNoticeDetailMapper;

    /**
     * 查询退货通知单明细列表
     */
    public List<ReturnNoticeDetailVo> queryReturnNoticeDetailList(QueryParamVO queryParamVO) {
        return returnNoticeDetailMapper.queryReturnNoticeDetail(queryParamVO);
    }

    /**
     * 根据退货通知单ID查询明细列表
     */
    public List<ReturnNoticeDetail> getDetailsByNoticeId(String noticeId) {
        return this.lambdaQuery()
                .eq(ReturnNoticeDetail::getNoticeId, noticeId)
                .list();
    }

    /**
     * 根据退货通知单ID和物料ID查询明细
     */
    public ReturnNoticeDetail getByNoticeIdAndMaterialId(String noticeId, String materialId) {
        try {
            return this.lambdaQuery()
                    .eq(ReturnNoticeDetail::getNoticeId, noticeId)
                    .eq(ReturnNoticeDetail::getMaterialId, materialId)
                    .one();
        } catch (Exception e) {
            log.warn("根据退货通知单ID和物料ID查询明细失败，退货通知单ID：{}，物料ID：{}，错误：{}", noticeId, materialId, e.getMessage());
            return null;
        }
    }
}
