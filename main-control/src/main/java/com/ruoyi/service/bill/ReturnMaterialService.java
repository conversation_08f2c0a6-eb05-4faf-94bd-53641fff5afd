package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.ReturnMaterial;
import com.ruoyi.domain.bill.ReturnMaterialDetail;
import com.ruoyi.mapper.bill.returnmaterial.ReturnMaterialMapper;
import com.ruoyi.mapper.bill.returnmaterial.ReturnMaterialDetailMapper;
import com.ruoyi.service.basicData.BasicDocumentInfoService;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.ReturnMaterialDetailVo;
import com.ruoyi.vo.bill.ReturnMaterialVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 退料申请单服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReturnMaterialService extends ServiceImpl<ReturnMaterialMapper, ReturnMaterial> {

    private static final Logger logger = LoggerFactory.getLogger(ReturnMaterialService.class);

    @Resource
    private ErpService erpService;

    @Resource
    private ReturnMaterialMapper returnMaterialMapper;

    @Resource
    private ReturnMaterialDetailMapper returnMaterialDetailMapper;

    @Resource
    private ReturnMaterialDetailService returnMaterialDetailService;

    @Resource
    private BasicDocumentInfoService basicDocumentInfoService;

    /**
     * 查询退料申请单列表
     */
    public List<ReturnMaterialVo> queryReturnMaterialList(QueryParamVO queryParamVO) {
        return returnMaterialMapper.queryReturnMaterial(queryParamVO);
    }

    /**
     * 查询退料申请单明细列表
     */
    public List<ReturnMaterialDetailVo> queryReturnMaterialDetail(QueryParamVO queryParamVO) {
        return returnMaterialDetailService.queryReturnMaterialDetailList(queryParamVO);
    }

    /**
     * 根据单据编号查询退料申请单
     */
    public ReturnMaterial getByBillNo(String billNo) {
        try {
            return this.lambdaQuery()
                    .eq(ReturnMaterial::getBillNo, billNo)
                    .one();
        } catch (Exception e) {
            logger.warn("根据单据编号查询退料申请单失败，单据编号：{}，错误：{}", billNo, e.getMessage());
            return null;
        }
    }

    /**
     * 从ERP同步退料申请单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP退料申请单...");

        // 1. 从ERP拉取所有退料申请单数据
        List<Map<String, Object>> erpDataList = fetchAllErpReturnMaterialData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到退料申请单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到退料申请单", null);
        }
        log.info("从ERP查询到 {} 条退料申请单行记录", erpDataList.size());

        // 2. 按单据ID进行分组
        Map<String, List<Map<String, Object>>> erpAppsGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.ID))));
        List<String> erpAppIds = new ArrayList<>(erpAppsGrouped.keySet());

        // 3. 查询现有的退料申请单
        Map<String, ReturnMaterial> existingAppsMap = new HashMap<>();
        if (!erpAppIds.isEmpty()) {
            List<ReturnMaterial> existingApps = this.lambdaQuery()
                    .in(ReturnMaterial::getId, erpAppIds)
                    .list();
            existingAppsMap = existingApps.stream()
                    .collect(Collectors.toMap(ReturnMaterial::getId, app -> app));
        }

        // 4. 准备数据集合
        List<ReturnMaterial> toInsertApps = new ArrayList<>();
        List<ReturnMaterial> toUpdateApps = new ArrayList<>();
        List<ReturnMaterialDetail> toInsertDetails = new ArrayList<>();

        // 5. 处理每个退料申请单
        for (String appId : erpAppIds) {
            List<Map<String, Object>> appRows = erpAppsGrouped.get(appId);
            if (appRows.isEmpty()) continue;

            // 使用第一行数据构建主表信息
            Map<String, Object> firstRow = appRows.get(0);
            ReturnMaterial app = mapToReturnMaterial(firstRow);

            if (existingAppsMap.containsKey(appId)) {
                toUpdateApps.add(app);
            } else {
                toInsertApps.add(app);
            }

            // 构建明细信息
            for (Map<String, Object> row : appRows) {
                ReturnMaterialDetail detail = mapToReturnMaterialDetail(row, appId);
                toInsertDetails.add(detail);
            }
        }

        // 6. 执行数据库操作
        int insertedApps = 0, updatedApps = 0, insertedDetails = 0;

        if (!toInsertApps.isEmpty()) {
            this.saveBatch(toInsertApps);
            insertedApps = toInsertApps.size();
            log.info("新增退料申请单 {} 条", insertedApps);
        }

        if (!toUpdateApps.isEmpty()) {
            this.updateBatchById(toUpdateApps);
            updatedApps = toUpdateApps.size();
            log.info("更新退料申请单 {} 条", updatedApps);
        }

        // 删除现有明细，重新插入（简化处理）
        if (!erpAppIds.isEmpty()) {
            LambdaQueryWrapper<ReturnMaterialDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(ReturnMaterialDetail::getAppId, erpAppIds);
            returnMaterialDetailService.remove(deleteWrapper);
        }

        if (!toInsertDetails.isEmpty()) {
            returnMaterialDetailService.saveBatch(toInsertDetails);
            insertedDetails = toInsertDetails.size();
            log.info("新增退料申请单明细 {} 条", insertedDetails);
        }

        // 7. 生成采购出库单
        int generatedPurchaseOutStocks = 0;
        for (ReturnMaterial app : toInsertApps) {
            try {
                basicDocumentInfoService.generatePurchaseOutStockFromReturnMaterial(app);
                generatedPurchaseOutStocks++;
            } catch (Exception e) {
                log.error("为退料申请单 {} 生成采购出库单失败: {}", app.getBillNo(), e.getMessage());
            }
        }

        String summary = String.format("同步完成。退料申请单[新增:%d, 更新:%d], 明细[新增:%d], 生成采购出库单[%d]",
                insertedApps, updatedApps, insertedDetails, generatedPurchaseOutStocks);
        log.info(summary);

        return ResponseResult.getSuccessResult(summary, "");
    }

    /**
     * 从ERP获取退料申请单数据
     */
    private List<Map<String, Object>> fetchAllErpReturnMaterialData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.RETURN_MATERIAL);
        queryReq.setFieldKeys(String.join(",",
                // Head
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.BILL_TYPE_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.DATE,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.PURCHASE_ORG_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.RM_TYPE,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.APP_ORG_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.RM_LOC,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.SUPPLIER_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.RM_MODE,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.REPLENISH_MODE,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.BUSINESS_TYPE,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.REMARKS,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.RM_REASON,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.APPLICANT_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Head.APP_DEPT_ID,
                // Entry
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.MATERIAL_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.MATERIAL_NAME,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.UOM,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.MR_APP_QTY,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.NOTE_M,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.UNIT_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.PRICE_UNIT_ID_F,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.PRICE_QTY_F,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.REPLENISH_QTY,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.PUR_UNIT_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.PUR_QTY,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.STOCK_ID,
                CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.STOCK_LOC_ID
        ));
        queryReq.setLimit(0);
        return erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_RETURN_MATERIAL);
    }

    /**
     * 将ERP数据映射为退料申请单实体
     */
    private ReturnMaterial mapToReturnMaterial(Map<String, Object> erpData) {
        ReturnMaterial app = new ReturnMaterial();

        app.setId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.ID)));
        app.setBillNo(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.BILL_NO)));
        app.setBillTypeId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.BILL_TYPE_ID)));

        // 处理日期字段
        Object dateObj = erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.DATE);
        if (dateObj != null) {
            app.setAppDate(new Date(Long.parseLong(dateObj.toString())));
        }

        app.setPurchaseOrgId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.PURCHASE_ORG_ID)));
        app.setRmType(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.RM_TYPE)));
        app.setAppOrgId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.APP_ORG_ID)));
        app.setRmLoc(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.RM_LOC)));
        app.setSupplierId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.SUPPLIER_ID)));
        app.setRmMode(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.RM_MODE)));
        app.setReplenishMode(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.REPLENISH_MODE)));
        app.setBusinessType(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.BUSINESS_TYPE)));
        app.setRemarks(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.REMARKS)));
        app.setRmReason(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.RM_REASON)));
        app.setApplicantId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.APPLICANT_ID)));
        app.setAppDeptId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Head.APP_DEPT_ID)));

        // 设置审计字段
        app.setCreateTime(new Date());
        app.setCreateName("ERP同步");
        app.setUpdateTime(new Date());
        app.setUpdateName("ERP同步");

        return app;
    }
    /**
     * 将ERP数据映射为退料申请单明细实体
     */
    private ReturnMaterialDetail mapToReturnMaterialDetail(Map<String, Object> erpData, String appId) {
        ReturnMaterialDetail detail = new ReturnMaterialDetail();

        detail.setId(UUID.randomUUID().toString());
        detail.setAppId(appId);
        detail.setMaterialId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.MATERIAL_ID)));
        detail.setMaterialName(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.MATERIAL_NAME)));
        detail.setUom(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.UOM)));

        // 处理数量字段
        Object mrAppQtyObj = erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.MR_APP_QTY);
        if (mrAppQtyObj != null) {
            detail.setMrAppQty(new BigDecimal(mrAppQtyObj.toString()));
        }

        detail.setNoteM(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.NOTE_M)));
        detail.setUnitId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.UNIT_ID)));
        detail.setPriceUnitIdF(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.PRICE_UNIT_ID_F)));

        Object priceQtyFObj = erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.PRICE_QTY_F);
        if (priceQtyFObj != null) {
            detail.setPriceQtyF(new BigDecimal(priceQtyFObj.toString()));
        }

        Object replenishQtyObj = erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.REPLENISH_QTY);
        if (replenishQtyObj != null) {
            detail.setReplenishQty(new BigDecimal(replenishQtyObj.toString()));
        }

        detail.setPurUnitId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.PUR_UNIT_ID)));

        Object purQtyObj = erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.PUR_QTY);
        if (purQtyObj != null) {
            detail.setPurQty(new BigDecimal(purQtyObj.toString()));
        }

        detail.setStockId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.STOCK_ID)));
        detail.setStockLocId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnMaterial.Entry.STOCK_LOC_ID)));

        // 设置审计字段
        detail.setCreateTime(new Date());
        detail.setCreateName("ERP同步");
        detail.setUpdateTime(new Date());
        detail.setUpdateName("ERP同步");

        return detail;
    }
}