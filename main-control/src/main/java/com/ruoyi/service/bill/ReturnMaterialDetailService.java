package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.ReturnMaterialDetail;
import com.ruoyi.mapper.bill.returnmaterial.ReturnMaterialDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReturnMaterialDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退料申请单明细服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReturnMaterialDetailService extends ServiceImpl<ReturnMaterialDetailMapper, ReturnMaterialDetail> {

    @Resource
    private ReturnMaterialDetailMapper returnMaterialDetailMapper;

    /**
     * 查询退料申请单明细列表
     */
    public List<ReturnMaterialDetailVo> queryReturnMaterialDetailList(QueryParamVO queryParamVO) {
        return returnMaterialDetailMapper.queryReturnMaterialDetailVo(queryParamVO);
    }

    /**
     * 根据退料申请单ID查询明细列表（使用LambdaQueryWrapper）
     */
    public List<ReturnMaterialDetail> getDetailsByAppId(String appId) {
        return this.lambdaQuery()
                .eq(ReturnMaterialDetail::getAppId, appId)
                .list();
    }

    /**
     * 根据退料申请单ID和物料ID查询明细
     */
    public ReturnMaterialDetail getByReturnIdAndMaterialId(String returnId, String materialId) {
        try {
            return this.lambdaQuery()
                    .eq(ReturnMaterialDetail::getAppId, returnId)
                    .eq(ReturnMaterialDetail::getMaterialId, materialId)
                    .one();
        } catch (Exception e) {
            log.warn("根据退料申请单ID和物料ID查询明细失败，退料申请单ID：{}，物料ID：{}，错误：{}", returnId, materialId, e.getMessage());
            return null;
        }
    }
}
