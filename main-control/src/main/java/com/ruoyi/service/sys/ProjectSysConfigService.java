package com.ruoyi.service.sys;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.sys.ProjectSysConfig;
import com.ruoyi.mapper.sys.IProjectSysConfigMapper;
import com.ruoyi.vo.basicData.SoftwareDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ProjectSysConfigService  extends ServiceImpl<IProjectSysConfigMapper, ProjectSysConfig> {

    @Resource
    private IProjectSysConfigMapper projectSysConfigMapper;

    public ProjectSysConfig getSysConfigByType(String config_type) {
        return this.projectSysConfigMapper.getSysConfigByType(config_type);
    }

    public ProjectSysConfig getSysConfigByVal(String config_val) {
        return this.projectSysConfigMapper.getSysConfigByVal(config_val);
    }

    public String getSysConfigValByType(String config_type) {
        return this.projectSysConfigMapper.getSysConfigValByType(config_type);
    }

    public void uptSysConfig(String config_value, String config_type) {
        this.projectSysConfigMapper.uptSysConfig(config_value,config_type);
    }

    public String getSysConfigTypeByVal(@Param("config_value")String config_value){
        return this.projectSysConfigMapper.getSysConfigTypeByVal(config_value);
    }

    public List<ProjectSysConfig> getSysConfigValListByType(String config_type) {
        QueryWrapper<ProjectSysConfig> queryWrapper = new QueryWrapper<ProjectSysConfig>();
        queryWrapper.eq("config_type",config_type);
        return this.projectSysConfigMapper.selectList(queryWrapper);
    }
}
