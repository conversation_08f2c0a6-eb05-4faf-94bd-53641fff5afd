package com.ruoyi.service.work;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.domain.basicData.BasicDocumentDetail;
import com.ruoyi.domain.basicData.BasicDocumentInfo;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.basicData.DocumentInventoryDetail;
import com.ruoyi.mapper.basicData.BasicDocumentDetailMapper;
import com.ruoyi.mapper.basicData.BasicDocumentInfoMapper;
import com.ruoyi.mapper.basicData.BasicMaterialBatchInventoryMapper;
import com.ruoyi.mapper.basicData.DocumentInventoryDetailMapper;
import com.ruoyi.service.basicData.UpperSignalDataService;
import com.ruoyi.service.sys.SysInterConfigService;
import com.ruoyi.utils.GsonUtils;
import com.ruoyi.utils.HttpUtils;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.utils.constant.InterConstant;
import com.ruoyi.vo.mes.*;
import com.ruoyi.vo.warehouse.MaterialInventoryDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class MesUpperService {
    @Resource
    BasicDocumentInfoMapper mesDocumentInfoMapper;

    @Resource
    BasicDocumentDetailMapper mesDocumentDetailMapper;

    @Resource
    BasicMaterialBatchInventoryMapper basicMaterialBatchInventoryMapper;

    @Resource
    DocumentInventoryDetailMapper documentInventoryDetailMapper;

    @Resource
    LkSystemService lkSystemService;

    @Resource
    SysInterConfigService sysInterConfigService;

    @Resource
    UpperSignalDataService upperSignalDataService;


    public ResponseResult reportDocument(MesDocumentReport mesDocumentReport) {
        String interConfigValByType = sysInterConfigService.getInterConfigValByType(InterConstant.InterType.MES_DOCUMENT_REPORT);
        ResponseResult responseResult = HttpUtils.sendRequestGetResponseResult(interConfigValByType, mesDocumentReport);
        upperSignalDataService.insertData(GsonUtils.toJsonString(mesDocumentReport),GsonUtils.toJsonString(responseResult),new Date(), CommonConstant.SignalType.WMS_REPORT_MES,"WMS",CommonConstant.IsWeb.AUTO);
        return responseResult;
    }

   /* @Transactional
    public ResponseResult receiveMesDocument(MesDocumentDto param) {
        BasicDocumentInfo mesDocumentInfo = mesDocumentInfoMapper.getDocumentInfoByCode(param.getTransactionCode());
        if (mesDocumentInfo != null){
            return ResponseResult.getErrorResult(param.getTransactionCode() + "单据已存在，请勿重复下发");
        }
        //保存单据
        BasicDocumentInfo mesDocumentInfo1 = new BasicDocumentInfo();
        String id = UUID.randomUUID().toString();
        mesDocumentInfo1.setId(id);
        mesDocumentInfo1.setBusinessType(param.getBusinessType());
        mesDocumentInfo1.setReceTime(param.getReceTime());
        mesDocumentInfo1.setTransactionCode(param.getTransactionCode());
        mesDocumentInfo1.setTransactionType(param.getTransactionType());
        mesDocumentInfo1.setStatus(CommonConstant.DocumentState.WAIT_CONFIRM);
        mesDocumentInfoMapper.insert(mesDocumentInfo1);
        List<DocumentDetailDto> details = param.getDetails();
        for (DocumentDetailDto documentDetailDto : details){
            BasicDocumentDetail mesDocumentDetail = new BasicDocumentDetail();
            String detailId = UUID.randomUUID().toString();
            mesDocumentDetail.setId(detailId);
            mesDocumentDetail.setQuantity(documentDetailDto.getQuantity());
            mesDocumentDetail.setCompletedNum(0);
            mesDocumentDetail.setMaterialCode(documentDetailDto.getMaterialCode());
            mesDocumentDetail.setDocumentCode(param.getTransactionCode());
            mesDocumentDetailMapper.insert(mesDocumentDetail);
            if (param.getTransactionType() == CommonConstant.InoutType.OUT){
                //查询物料批次
                List<BasicMaterialBatchInventory> availBatchByMaterialCode = new ArrayList<>();
                availBatchByMaterialCode = basicMaterialBatchInventoryMapper.getAvailBatchByMaterialCode(documentDetailDto.getMaterialCode());
                //查询立库数据
                Integer boxInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.BOX_INVENTORY_STATISTICS, documentDetailDto.getMaterialCode());
                Integer plateInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.PLATE_INVENTORY_STATISTICS, documentDetailDto.getMaterialCode());
                Integer profileInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.PROFILE_INVENTORY_STATISTICS, documentDetailDto.getMaterialCode());
                this.inventoryAddLk(availBatchByMaterialCode,boxInteger,CommonConstant.LkName.BOX);
                this.inventoryAddLk(availBatchByMaterialCode,plateInteger,CommonConstant.LkName.PLATE);
                this.inventoryAddLk(availBatchByMaterialCode,profileInteger,CommonConstant.LkName.PROFILE);
                // 遍历已排序的批次列表
                int remainingQty = documentDetailDto.getQuantity();
                List<DocumentInventoryDetail> allocations = new ArrayList<>();
                for (BasicMaterialBatchInventory batch : availBatchByMaterialCode) {
                    if (remainingQty <= 0) break;
                    // 计算当前批次可分配的数量
                    int allocatableQty = Math.min(remainingQty, batch.getAvailNum());
                    if (allocatableQty > 0) {
                        // 创建分配记录
                        DocumentInventoryDetail documentInventoryDetail = new DocumentInventoryDetail();
                        documentInventoryDetail.setId(UUID.randomUUID().toString());
                        documentInventoryDetail.setDetailCode(detailId);
                        documentInventoryDetail.setMaterialCode(documentDetailDto.getMaterialCode());
                        documentInventoryDetail.setQuantity(allocatableQty);
                        if (StringUtils.isNotEmpty(batch.getId())){
                            documentInventoryDetail.setContainerCode(batch.getId());
                            //冻结库存
                            batch.setAvailNum(batch.getAvailNum() - allocatableQty);
                            batch.setFreezeNum(batch.getFreezeNum() + allocatableQty);
                            basicMaterialBatchInventoryMapper.updateByPrimaryKeySelective(batch);
                        }else {
                            documentInventoryDetail.setContainerCode(batch.getContainerCode());
                        }
                        documentInventoryDetailMapper.insert(documentInventoryDetail);
                        // 更新剩余需求量
                        remainingQty -= allocatableQty;
                    }
                }
            }
        }
        return ResponseResult.getSuccessResult();
    }*/

    private void inventoryAddLk(List<BasicMaterialBatchInventory> availBatchByMaterialCode, Integer num, String lkName) {
        if (num != null && num > 0){
            BasicMaterialBatchInventory basicMaterialBatchInventory = new BasicMaterialBatchInventory();
            basicMaterialBatchInventory.setAvailNum(num);
            basicMaterialBatchInventory.setContainerCode(lkName);
            availBatchByMaterialCode.add(basicMaterialBatchInventory);
        }
    }

    public ResponseResult materialInventoryNum(MaterialNumRequest param) {
        //查询库存数量
        Integer boxInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.BOX_INVENTORY_STATISTICS, param.getMaterialCode());
        Integer plateInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.PLATE_INVENTORY_STATISTICS, param.getMaterialCode());
        Integer profileInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.PROFILE_INVENTORY_STATISTICS, param.getMaterialCode());
        MaterialInventoryDto materialInventoryDto = basicMaterialBatchInventoryMapper.queryInventoryByCode(param.getMaterialCode());
        Integer num = materialInventoryDto.getAvailNum();
        num = num + boxInteger + plateInteger + profileInteger;
        MaterialNumResponse materialNumResponse = new MaterialNumResponse();
        materialNumResponse.setMaterialCode(param.getMaterialCode());
        materialNumResponse.setAvailableQty(num);
        return ResponseResult.getSuccessResult("success",materialNumResponse);
    }
}
