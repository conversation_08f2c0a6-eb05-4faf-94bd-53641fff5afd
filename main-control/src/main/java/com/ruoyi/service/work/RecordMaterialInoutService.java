package com.ruoyi.service.work;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.RecordInoutDetail;
import com.ruoyi.domain.basicData.RecordMaterialInout;
import com.ruoyi.mapper.basicData.BasicWarehouseContainerMapper;
import com.ruoyi.mapper.basicData.RecordMaterialInoutMapper;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.report.RecordMaterialInoutVo;
import com.ruoyi.vo.warehouse.ContainerLocationInfoDto;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
public class RecordMaterialInoutService extends ServiceImpl<RecordMaterialInoutMapper, RecordMaterialInout> {

    @Resource
    private RecordMaterialInoutMapper recordMaterialInoutMapper;
    @Resource
    BasicWarehouseContainerMapper basicWarehouseContainerMapper;

    public void updateByPrimaryKey(RecordMaterialInout record) {
         recordMaterialInoutMapper.updateByPrimaryKey(record);
    }

    public void insertByRecordInoutDetail(RecordMaterialInout recordInoutDetail) {
        recordInoutDetail.setId(UUID.randomUUID().toString());
        recordInoutDetail.setRecorder(SecurityUtils.getUsername());
        recordInoutDetail.setRecordDate(DateAndTimeUtil.getNowDate());
        if(recordInoutDetail.getDataOrigin() == null){
            recordInoutDetail.setDataOrigin(CommonConstant.BusinessSource.WEB);
        }
        recordMaterialInoutMapper.insert(recordInoutDetail);
    }

    /**
     * 新增物料出入库记录
     * @param recordInoutDetails 出入库记录详情
     * @param inoutType 单据类型
     * @param dataOrigin 数据来源
     */
    public void insertByRecordInoutDetailList(List<RecordInoutDetail> recordInoutDetails, Integer inoutType, Integer dataOrigin, Date lockTime, String checker) {
        List<RecordMaterialInout> recordMaterialInoutList = new ArrayList<>();
        for (RecordInoutDetail recordInoutDetail : recordInoutDetails) {
            RecordMaterialInout recordMaterialInout = new RecordMaterialInout();
            BeanUtils.copyProperties(recordInoutDetail, recordMaterialInout);
            recordMaterialInout.setId(UUID.randomUUID().toString());
            recordMaterialInout.setInoutType(inoutType);
            recordMaterialInout.setRecordDate(new Date());
            recordMaterialInout.setDataOrigin(dataOrigin);
            recordMaterialInout.setInoutType(inoutType);
            recordMaterialInout.setRecordDate(DateAndTimeUtil.getNowDate());
            recordMaterialInout.setDataOrigin(dataOrigin);
            recordMaterialInout.setRecorder(checker);
            recordMaterialInout.setLockTime(lockTime);
            recordMaterialInout.setTotalNum(recordInoutDetail.getMaterialNum());
            recordMaterialInoutList.add(recordMaterialInout);
        }
        saveBatch(recordMaterialInoutList);
    }

    /**
     * 新增物料出入库记录
     */
    public void addBatchInout(List<RecordMaterialInout> recordMaterialInoutList) {

        saveBatch(recordMaterialInoutList);
    }

    /**
     * 查询物料出入库记录
     */
    public List<RecordMaterialInoutVo> queryRecordMaterialInout(QueryParamVO queryParamVO) {
        List<RecordMaterialInoutVo> recordMaterialInoutVos = recordMaterialInoutMapper.queryRecordMaterialInout(queryParamVO);
        //补充位置信息
        for (RecordMaterialInoutVo recordMaterialInoutVo : recordMaterialInoutVos) {
            ContainerLocationInfoDto containerLocationInfoDto = basicWarehouseContainerMapper.queryContainerLocationInfo(recordMaterialInoutVo.getContainerCode());
            if (containerLocationInfoDto != null) {
                recordMaterialInoutVo.setMaterialLocation(containerLocationInfoDto.getWarehouseName() + "-" + containerLocationInfoDto.getShelfName() + "-" + containerLocationInfoDto.getLevelName() + "-" + containerLocationInfoDto.getPositionName());
            }
        }
        return recordMaterialInoutVos;
    }
}
