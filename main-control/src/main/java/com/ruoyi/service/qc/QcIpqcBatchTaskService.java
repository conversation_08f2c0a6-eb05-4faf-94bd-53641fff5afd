package com.ruoyi.service.qc;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.basicData.BasicDocumentInfo;
import com.ruoyi.domain.qc.QcIpqcBatchTask;
import com.ruoyi.domain.qc.QcIpqcTaskInfo;
import com.ruoyi.mapper.qc.QcIpqcBatchTaskMapper;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.ResponseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.util.Date;
import java.util.UUID;


/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 11:45
 * @Description: 类描述
 */
@Service
public class QcIpqcBatchTaskService extends ServiceImpl<QcIpqcBatchTaskMapper, QcIpqcBatchTask> {

    private static final Logger logger = LoggerFactory.getLogger(QcIpqcBatchTaskService.class);

    @Resource
    private QcIpqcBatchTaskMapper qcIpqcBatchTaskMapper;

    public synchronized String getMaxIndex(String headstr) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        QueryWrapper<QcIpqcBatchTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("batch_task", headstr + strDate);
        queryWrapper.orderByDesc("batch_task");
        queryWrapper.last("LIMIT 1");
        QcIpqcBatchTask task = this.qcIpqcBatchTaskMapper.selectOne(queryWrapper);
        String mxstr;
        if (task == null) {
            mxstr = headstr + DateAndTimeUtil.getNowTimeNoSeparator();
        }else {
            mxstr = task.getBatchTask();
        }
        return an.getNum(headstr, mxstr);
    }



    /**
     * 更新质检详情
     */
    public void addOrUptQcIpqcInfo(QcIpqcBatchTask qcIpqcBatchTask) {
        QcIpqcBatchTask reQcIpqcBatchTask = queryQcIpqcBatchTask(qcIpqcBatchTask.getBatchTask());
        if (reQcIpqcBatchTask == null) {
            qcIpqcBatchTask.setId(UUID.randomUUID().toString());
            qcIpqcBatchTask.setCreateTime(new Date());
            this.qcIpqcBatchTaskMapper.insert(qcIpqcBatchTask);
        } else {
            qcIpqcBatchTask.setId(reQcIpqcBatchTask.getId());
            qcIpqcBatchTask.setUpdateTime(new Date());
            this.qcIpqcBatchTaskMapper.updateById(qcIpqcBatchTask);
        }
    }

    /**
     * 根据任务号查询批量数据
     */
    public QcIpqcBatchTask queryQcIpqcBatchTask(String batchTask) {
        LambdaQueryWrapper<QcIpqcBatchTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QcIpqcBatchTask::getBatchTask, batchTask)
                .orderByDesc(QcIpqcBatchTask::getUpdateTime)
                .last("LIMIT 1");
        return qcIpqcBatchTaskMapper.selectOne(wrapper);
    }

    /**
     * 根据任务号查询批量数据
     */
    public QcIpqcBatchTask queryQcIpqcByipqcCodes(String ipqcCodes) {
        LambdaQueryWrapper<QcIpqcBatchTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QcIpqcBatchTask::getIpqcCodes, ipqcCodes)
                .orderByDesc(QcIpqcBatchTask::getUpdateTime)
                .last("LIMIT 1");
        return qcIpqcBatchTaskMapper.selectOne(wrapper);
    }
}
