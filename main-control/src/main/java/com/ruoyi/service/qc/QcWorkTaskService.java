package com.ruoyi.service.qc;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.domain.basicData.BasicMaterialInfo;
import com.ruoyi.domain.qc.*;
import com.ruoyi.service.basicData.BasicMaterialInfoService;
import com.ruoyi.service.work.ReportService;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.qc.BatchHandIpqcInfo;
import com.ruoyi.vo.qc.HandIpqcDetail;
import com.ruoyi.vo.qc.HandIpqcInfo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 18:28
 * @Description: 质检任务服务层
 */
@Service
public class QcWorkTaskService {

    protected final Logger logger = LoggerFactory.getLogger(QcWorkTaskService.class);

    @Resource
    private QcIpqcInfoService qcIpqcInfoService;
    @Resource
    private QcIpqcDetailInfoService qcIpqcDetailInfoService;
    @Resource
    private QcTemplateInfoService qcTemplateInfoService;
    @Resource
    private QcTemplateDetailService qcTemplateDetailService;
    @Resource
    private QcTemplateItemService qcTemplateItemService;
    @Resource
    private QcTemplateMaterialService qcTemplateMaterialService;
    @Resource
    private BasicMaterialInfoService basicMaterialInfoService;
    @Resource
    private QcIpqcBatchTaskService qcIpqcBatchTaskService;

    /**
     * 增加质检任务
     */
    public ResponseResult addQcIpqcTaskInfo(QcIpqcTaskInfo qcIpqcTaskInfo) {
        Integer qcType = qcIpqcTaskInfo.getQcType();
        String materialCode = qcIpqcTaskInfo.getMaterialCode();
        BasicMaterialInfo basicMaterialInfo = basicMaterialInfoService.queryBasicMaterialByCode(materialCode);
        if (basicMaterialInfo == null) {
            String msg = "根据物料编码:" + materialCode + " 查询物料信息失败!";
            logger.info(msg);
            return ResponseResult.getErrorResult(msg);
        }
        QcTemplateMaterial qcTemplateMaterial = qcTemplateMaterialService.getTemplateMaterialByMaterialCodeAndQcType(materialCode, qcType);
        if (qcTemplateMaterial == null) {
            String msg = materialCode + " 物料编码未配置质检模板数据!";
            logger.info(msg);
            return ResponseResult.getErrorResult(msg);
        }
        String templateCode = qcTemplateMaterial.getTemplateCode();
        List<QcTemplateDetail> templateDetails = qcTemplateDetailService.getQcTemplateDetailByTemplateCode(templateCode);
        if (templateDetails == null || templateDetails.size() == 0) {
            String msg = materialCode + " 物料编码中的" + templateCode + " 质检模板未配置检验项!";
            logger.info(msg);
            return ResponseResult.getErrorResult(msg);
        }
        qcIpqcTaskInfo.setTemplateCode(templateCode);
        if (qcIpqcTaskInfo.getIpqcName() == null) {
            qcIpqcTaskInfo.setIpqcName(basicMaterialInfo.getMaterialName() + "质检单");
        }
        qcIpqcTaskInfo.setCreateBy(SecurityUtils.getUsername());
        qcIpqcTaskInfo.setCreateTime(new Date());
        //保存质检任务
        qcIpqcInfoService.addQcIpqcInfo(qcIpqcTaskInfo);
        //保存详情
        dealIpqcDetailInfo(qcIpqcTaskInfo.getIpqcCode(), qcTemplateMaterial.getId(), templateDetails);
        return ResponseResult.getSuccessResult();
    }

    //保存详情
    private void dealIpqcDetailInfo(String ipqcCode, String id, List<QcTemplateDetail> templateDetails) {
        for (QcTemplateDetail templateDetail : templateDetails) {
            QcIpqcDetailInfo qcIpqcDetailInfo = new QcIpqcDetailInfo();
            qcIpqcDetailInfo.setIpqcCode(ipqcCode);
            qcIpqcDetailInfo.setItemCode(templateDetail.getItemCode());
            qcIpqcDetailInfo.setCrQuantity(0);
            qcIpqcDetailInfo.setMajQuantity(0);
            qcIpqcDetailInfo.setMinQuantity(0);
            qcIpqcDetailInfo.setTemplateDetailId(templateDetail.getId());
            qcIpqcDetailInfo.setCreateBy(SecurityUtils.getUsername());
            qcIpqcDetailInfo.setCreateTime(new Date());
            qcIpqcDetailInfoService.addQcIpqcDetailInfo(qcIpqcDetailInfo);
        }
    }

    /**
     * 人工质检
     */
    public ResponseResult handQcTask(HandIpqcInfo handIpqcInfo) {
        Date currentTime = new Date();
        QcIpqcTaskInfo qcTaskInfo = qcIpqcInfoService.getById(handIpqcInfo.getId());
        qcTaskInfo.setQuantityUnqualified(handIpqcInfo.getQuantityUnqualified());
        qcTaskInfo.setQuantityQualified(handIpqcInfo.getQuantityQualified());
        qcTaskInfo.setRemark(handIpqcInfo.getRemark());
        qcTaskInfo.setUpdateBy(SecurityUtils.getUsername());
        qcTaskInfo.setCheckResult(handIpqcInfo.getCheckResult());
        qcTaskInfo.setStatus(handIpqcInfo.getStatus());
        qcTaskInfo.setUpdateTime(currentTime);
        qcIpqcInfoService.uptQcIpqcInfo(qcTaskInfo);

        for (HandIpqcDetail detail : handIpqcInfo.getDetails()) {
            QcIpqcDetailInfo qcIpqcDetailInfo = qcIpqcDetailInfoService.getById(detail.getId());
            qcIpqcDetailInfo.setCrQuantity(detail.getCrQuantity());
            qcIpqcDetailInfo.setMajQuantity(detail.getMajQuantity());
            qcIpqcDetailInfo.setMinQuantity(detail.getMinQuantity());
            qcIpqcDetailInfo.setUpdateBy(SecurityUtils.getUsername());
            qcIpqcDetailInfo.setQcVal(detail.getQcVal());
            qcIpqcDetailInfo.setFileName(detail.getFileName());
            qcIpqcDetailInfo.setRemark(detail.getRemark());
            qcIpqcDetailInfo.setUpdateTime(currentTime);
            qcIpqcDetailInfoService.uptQcIpqcDetailInfo(qcIpqcDetailInfo);
        }
        return ResponseResult.getSuccessResult();
    }



    /**
     * 批量质检
     */
    public ResponseResult dealBatchQcTask(BatchHandIpqcInfo param) {
        Date currentTime = new Date();
        List<QcIpqcTaskInfo> qcTaskInfoList = qcIpqcInfoService.queryQcIpqcInfoByCodes(param.getIpqcCodeList());
        //检查批量质检物料是否一致
        if (qcTaskInfoList == null || qcTaskInfoList.isEmpty()) {
            return ResponseResult.getErrorResult("质检任务列表不能为空！");
        }
        String firstMaterialCode = qcTaskInfoList.get(0).getMaterialCode();
        for (QcIpqcTaskInfo qcIpqcTaskInfo : qcTaskInfoList) {
            String currentMaterialCode = qcIpqcTaskInfo.getMaterialCode();
            if (!firstMaterialCode.equals(currentMaterialCode)) {
                return ResponseResult.getErrorResult("请选择相同物料进行批量质检！");
            }
        }
        //采用最新模板进行质检
        Set<String> templateCodes = new HashSet<>();
        for (QcIpqcTaskInfo qcIpqcTaskInfo : qcTaskInfoList) {
            templateCodes.add(qcIpqcTaskInfo.getTemplateCode());
        }
        QcTemplateInfo  latestTemplate = qcTemplateInfoService.getLatestTemplateByList(templateCodes);
        QcIpqcBatchTask qcIpqcBatchTask = new QcIpqcBatchTask();

        String ipqcCodes = String.join(",", param.getIpqcCodeList());
        qcIpqcBatchTask.setIpqcCodes(ipqcCodes);
        QcIpqcBatchTask localQcIpqcBatchTask = qcIpqcBatchTaskService.queryQcIpqcByipqcCodes(ipqcCodes);
        String batchTask;
        if(localQcIpqcBatchTask != null){
            batchTask = localQcIpqcBatchTask.getBatchTask();
        }else {
            batchTask =  qcIpqcBatchTaskService.getMaxIndex(CommonConstant.CodePrefix.BATCH_QUALITY);
        }
        for (QcIpqcTaskInfo qcIpqcTaskInfo : qcTaskInfoList) {
            qcIpqcTaskInfo.setBatchTask(batchTask);
            qcIpqcTaskInfo.setTemplateCode(latestTemplate.getTemplateCode());
            qcIpqcTaskInfo.setUpdateTime(currentTime);
            qcIpqcTaskInfo.setRemark(param.getRemark());
            qcIpqcTaskInfo.setUpdateBy(param.getUpdateBy());
            qcIpqcInfoService.uptQcIpqcInfo(qcIpqcTaskInfo);
        }
        qcIpqcBatchTask.setBatchTask(batchTask);
        qcIpqcBatchTask.setCheckResult(param.getCheckResult());
        qcIpqcBatchTask.setTotalQualifiedNum(param.getTotalQualifiedNum());
        qcIpqcBatchTask.setTotalUnqualifiedNum(param.getTotalUnqualifiedNum());
        qcIpqcBatchTask.setTotalCheckNum(param.getTotalCheckNum());
        qcIpqcBatchTask.setUpdateTime(currentTime);
        qcIpqcBatchTaskService.addOrUptQcIpqcInfo(qcIpqcBatchTask);

        for (HandIpqcDetail detail : param.getDetails()) {
            QcIpqcDetailInfo qcIpqcDetailInfo = qcIpqcDetailInfoService.getById(detail.getId());
            qcIpqcDetailInfo.setCrQuantity(detail.getCrQuantity());
            qcIpqcDetailInfo.setMajQuantity(detail.getMajQuantity());
            qcIpqcDetailInfo.setMinQuantity(detail.getMinQuantity());
            qcIpqcDetailInfo.setUpdateBy(SecurityUtils.getUsername());
            qcIpqcDetailInfo.setQcVal(detail.getQcVal());
            qcIpqcDetailInfo.setFileName(detail.getFileName());
            qcIpqcDetailInfo.setRemark(detail.getRemark());
            qcIpqcDetailInfo.setUpdateTime(currentTime);
            qcIpqcDetailInfoService.uptQcIpqcDetailInfo(qcIpqcDetailInfo);
        }
        return ResponseResult.getSuccessResult();
    }
    /**
     * 保存质检任务
     * materialCode 物料编码
     * quantityCheck 质检数量
     * companyCode 客户\供应商编码
     * sourceTaskType 源任务类型（可填默认值）
     * sourceTaskNo 来源单据
     * qcType 质检类型  1：来料质检， 4：出库质检
     */
    public void saveQcIpqcInfo(String materialCode, Integer quantityCheck, String companyCode, String sourceTaskType,
                               String sourceTaskNo, Integer qcType) {
        QcIpqcTaskInfo qcIpqcTaskInfo = new QcIpqcTaskInfo();
        qcIpqcTaskInfo.setMaterialCode(materialCode);
        qcIpqcTaskInfo.setQuantityCheck(quantityCheck);
        qcIpqcTaskInfo.setCompanyCode(companyCode);
        qcIpqcTaskInfo.setSourceTaskType(sourceTaskType);
        qcIpqcTaskInfo.setSourceTaskNo(sourceTaskNo);
        qcIpqcTaskInfo.setQcType(qcType);
        addQcIpqcTaskInfo(qcIpqcTaskInfo);
    }
}
