package com.ruoyi.service.common;

import com.ruoyi.domain.basicData.BasicSignalType;
import com.ruoyi.mapper.basicData.BasicSignalTypeMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/4/19 10:46
 * @Description: 类描述
 */
@Service
public class BasicSignalTypeService {

    @Resource
    private BasicSignalTypeMapper basicSignalTypeMapper;

    /**
     * 获取信号类型
     */
    public List<BasicSignalType> getBasicSignalTypeList() {
        return basicSignalTypeMapper.getBasicSignalTypeList();
    }

}
