package com.ruoyi.controller.qc;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.qc.QcIpqcDetailInfo;
import com.ruoyi.domain.qc.QcIpqcTaskInfo;
import com.ruoyi.service.qc.QcIpqcDetailInfoService;
import com.ruoyi.service.qc.QcIpqcInfoService;
import com.ruoyi.service.qc.QcWorkTaskService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.qc.BatchHandIpqcInfo;
import com.ruoyi.vo.qc.HandIpqcInfo;
import com.ruoyi.vo.qc.QcIpqcDetailInfoVo;
import com.ruoyi.vo.qc.QcIpqcInfoVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/06/17 14:57
 * @Description: 质检管理-质检任务
 */
@RestController
@RequestMapping("/speedbot/qc/task")
public class QualityTaskController extends BaseController {
    protected final Logger logger = LoggerFactory.getLogger(QualityTaskController.class);

    @Resource
    private QcIpqcInfoService qcIpqcInfoService;
    @Resource
    private QcIpqcDetailInfoService qcIpqcDetailInfoService;
    @Resource
    private QcWorkTaskService qcWorkTaskService;


    /**
     * 质检任务
     */
    @PostMapping("/queryQcIpqcInfo")
    public TableDataInfo queryQcIpqcInfo(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<QcIpqcInfoVo> list = this.qcIpqcInfoService.queryQcIpqcInfo(queryParamVO);
        return this.getDataTable(list);
    }

    @Log(title = "质检任务", businessType = BusinessType.INSERT)
    @PostMapping("/addQcIpqcTaskInfo")
    public ResponseResult addQcIpqcTaskInfo(@RequestBody QcIpqcTaskInfo qcIpqcTaskInfo){
        return this.qcWorkTaskService.addQcIpqcTaskInfo(qcIpqcTaskInfo);
    }

    @Log(title = "人工质检", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/handQcTask")
    public ResponseResult handQcTask(@RequestBody HandIpqcInfo handIpqcInfo){
        return this.qcWorkTaskService.handQcTask(handIpqcInfo);
    }

    @Log(title = "质检任务", businessType = BusinessType.DELETE)
    @PostMapping("/deleteQcIpqcInfo")
    public ResponseResult deleteQcIpqcInfo(@RequestBody BatchIdsReq req){
        return this.qcIpqcInfoService.deleteQcIpqcInfo(req);
    }

    /**
     * 质检任务详情
     */
    @PostMapping("/queryQcIpqcDetailInfo")
    public TableDataInfo queryQcIpqcDetailInfo(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<QcIpqcDetailInfoVo> list = this.qcIpqcDetailInfoService.queryQcIpqcDetailInfo(queryParamVO);
        return this.getDataTable(list);
    }

    @Log(title = "质检任务详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/uptQcIpqcDetailInfo")
    public ResponseResult uptQcIpqcDetailInfo(@RequestBody QcIpqcDetailInfo qcIpqcDetailInfo){
        return this.qcIpqcDetailInfoService.uptQcIpqcDetailInfo(qcIpqcDetailInfo);
    }

    @Log(title = "批量处理质检", businessType = BusinessType.OTHER)
    @RepeatSubmit
    @PostMapping("/batchQcTask")
    public ResponseResult batchQcTask(@RequestBody BatchHandIpqcInfo param){
        return qcWorkTaskService.dealBatchQcTask(param);
    }

}
