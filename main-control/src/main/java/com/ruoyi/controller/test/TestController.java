package com.ruoyi.controller.test;

import com.ruoyi.service.basicData.*;
import com.ruoyi.service.bill.*;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.basicData.BasicDocumentInfoDto;
import com.ruoyi.vo.basicData.DocumentDetailDto;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/speedbot/test")
public class TestController {
    protected final Logger logger = LoggerFactory.getLogger(TestController.class);

    @Autowired
    private ErpService erpService;

    @Autowired
    private BasicMaterialInfoService basicMaterialInfoService;

    @Resource
    private BasicMaterialClassifyService basicMaterialClassifyService;

    @Resource
    private BasicCompanyInfoService basicCompanyInfoService;

    @Resource
    private BasicCompanyGroupService basicCompanyGroupService;

    @Resource
    private BasicContactInfoService basicContactInfoService;

    @Resource
    private BasicUnitInfoService basicUnitInfoService;

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @Resource
    private DeliveryNoticeService deliveryNoticeService;

    @Resource
    private ProductionPickingMaterialService productionPickingMaterialService;

    @Resource
    private ProductionFeedMaterialService productionFeedMaterialService;

    @Resource
    private ProductionInStockService productionInStockService;

    @Resource
    private SimpleProductionPickingMaterialService simpleProductionPickingMaterialService;

    @Resource
    private SimpleProductionInStockService simpleProductionInStockService;

    @Resource
    private ReturnMaterialService returnMaterialService;
    @Resource
    private ReceiveNoticeService receiveNoticeService;

    @Resource
    private ReturnNoticeService returnNoticeService;

    @Resource
    private BasicDocumentInfoService basicDocumentInfoService;

    /**
     * 从ERP同步物料信息
     */
    @PostMapping("/syncErpMaterials")
    public ResponseResult syncErpMaterials() throws Exception {
        logger.info("开始从ERP同步物料信息...");
        ResponseResult result = basicMaterialInfoService.syncFromErp();
        logger.info("物料信息同步完成。");
        return result;
    }

    /**
     * 从ERP同步物料分类信息
     * @return 同步结果
     */
    @PostMapping("/syncErpMaterialGroups")
    public ResponseResult syncErpMaterialGroups() throws Exception {
        logger.info("开始同步ERP物料分类数据...");
        ResponseResult result = basicMaterialClassifyService.syncFromErpGroups();
        logger.info("同步ERP物料分类数据结束。");
        return result;
    }

    /**
     * 从ERP同步客户信息
     * @return 同步结果
     */
    @PostMapping("/syncErpCustomers")
    public ResponseResult syncErpCustomers() throws Exception {
        return basicCompanyInfoService.syncFromErpCustomers();
    }

    /**
     * 从ERP同步供应商信息
     * @return 同步结果
     */
    @PostMapping("/syncErpSuppliers")
    public ResponseResult syncErpSuppliers() throws Exception {
        return basicCompanyInfoService.syncFromErpSuppliers();
    }

    /**
     * 从ERP同步客户分组信息
     * @return 同步结果
     */
    @PostMapping("/syncErpCustomerGroups")
    public ResponseResult syncErpCustomerGroups() throws Exception {
        return basicCompanyGroupService.syncFromErpCustomerGroups();
    }

    /**
     * 从ERP同步供应商分组信息
     * @return 同步结果
     */
    @PostMapping("/syncErpSupplierGroups")
    public ResponseResult syncErpSupplierGroups() throws Exception {
        return basicCompanyGroupService.syncFromErpSupplierGroups();
    }

    /**
     * 从ERP同步联系人信息
     * @return 同步结果
     */
    @PostMapping("/syncErpContacts")
    public ResponseResult syncErpContacts() throws Exception {
        logger.info("开始同步ERP联系人数据...");
        ResponseResult result = basicContactInfoService.syncFromErp();
        logger.info("同步ERP联系人数据结束。");
        return result;
    }

    /**
     * 从ERP同步计量单位信息
     * @return 同步结果
     */
    @PostMapping("/syncErpUnits")
    public ResponseResult syncErpUnits() throws Exception {
        logger.info("开始同步ERP计量单位数据...");
        ResponseResult result = basicUnitInfoService.syncFromErp();
        logger.info("同步ERP计量单位数据结束。");
        return result;
    }

    /**
     * 从ERP同步采购订单信息
     * @return 同步结果
     */
    @PostMapping("/syncErpPurchaseOrders")
    public ResponseResult syncErpPurchaseOrders() throws Exception {
        logger.info("开始同步ERP采购订单数据...");
        ResponseResult result = purchaseOrderService.syncFromErp();
        logger.info("同步ERP采购订单数据结束。");
        return result;
    }
      /**
     * 从ERP同步发货通知单
     */
    @PostMapping("/syncErpDeliveryNotices")
    public ResponseResult syncFromErp() {
        try {
            return deliveryNoticeService.syncFromErp();
        } catch (Exception e) {
            logger.error("从ERP同步发货通知单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步生产领料单
     */
    @PostMapping("/syncErpProductionPickingMaterials")
    public ResponseResult syncErpProductionPickingMaterials() {
        try {
            logger.info("开始同步ERP生产领料单数据...");
            ResponseResult result = productionPickingMaterialService.syncFromErp();
            logger.info("同步ERP生产领料单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步生产领料单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步生产补料单
     */
    @PostMapping("/syncErpProductionFeedMaterials")
    public ResponseResult syncErpProductionFeedMaterials() {
        try {
            logger.info("开始同步ERP生产补料单数据...");
            ResponseResult result = productionFeedMaterialService.syncFromErp();
            logger.info("同步ERP生产补料单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步生产补料单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步生产入库单
     */
    @PostMapping("/syncErpProductionInStocks")
    public ResponseResult syncErpProductionInStocks() {
        try {
            logger.info("开始同步ERP生产入库单数据...");
            ResponseResult result = productionInStockService.syncFromErp();
            logger.info("同步ERP生产入库单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步生产入库单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步简单生产领料单
     */
    @PostMapping("/syncErpSimpleProductionPickingMaterials")
    public ResponseResult syncErpSimpleProductionPickingMaterials() {
        try {
            logger.info("开始同步ERP简单生产领料单数据...");
            ResponseResult result = simpleProductionPickingMaterialService.syncFromErp();
            logger.info("同步ERP简单生产领料单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步简单生产领料单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步简单生产入库单
     */
    @PostMapping("/syncErpSimpleProductionInStocks")
    public ResponseResult syncErpSimpleProductionInStocks() {
        try {
            logger.info("开始同步ERP简单生产入库单数据...");
            ResponseResult result = simpleProductionInStockService.syncFromErp();
            logger.info("同步ERP简单生产入库单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步简单生产入库单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 测试采购订单下推采购入库单
     */
    @PostMapping("/testPushPurchaseOrderToInStock")
    public ResponseResult testPushPurchaseOrderToInStock() {
        logger.info("开始测试单个采购订单下推采购入库单...");
        // 使用测试数据：101CGDD003027
        String orderNumber = "101CGDD003027";
        boolean result = erpService.pushSinglePurchaseOrderToInStock(orderNumber);

        if (result) {
            logger.info("单个采购订单下推采购入库单测试成功");
            return ResponseResult.getSuccessResult();
        } else {
            logger.error("单个采购订单下推采购入库单测试失败");
            return ResponseResult.getErrorResult("单个下推失败");
        }
    }



    /**
     * 测试ERP同步功能
     */
    @PostMapping("/testReceiveNoticeSync")
    public ResponseResult testReceiveNoticeSync() {
        try {
            logger.info("开始测试收料通知单ERP同步功能");
            ResponseResult result = receiveNoticeService.syncFromErp();
            logger.info("测试完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("测试收料通知单同步失败", e);
            return ResponseResult.getErrorResult("测试失败: " + e.getMessage());
        }
    }
    /**
     * 测试ERP同步功能
     */
    @PostMapping("/testReturnMaterialSync")
    public ResponseResult testReturnMaterialSync() {
        try {
            logger.info("开始测试退料申请单ERP同步功能");
            ResponseResult result = returnMaterialService.syncFromErp();
            logger.info("测试完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("测试退料申请单同步失败", e);
            return ResponseResult.getErrorResult("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试退货通知单ERP同步功能
     */
    @PostMapping("/testReturnNoticeSync")
    public ResponseResult testReturnNoticeSync() {
        try {
            logger.info("开始测试退货通知单ERP同步功能");
            ResponseResult result = returnNoticeService.syncFromErp();
            logger.info("测试完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("测试退货通知单同步失败", e);
            return ResponseResult.getErrorResult("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试出库单据新增时的容器处理功能
     * description: 测试手动指定容器和自动分配容器的功能
     * @author: sqpeng
     * @param request 测试请求参数
     * @return {@link ResponseResult}
     */
    @PostMapping("/testOutboundDocumentWithContainer")
    public ResponseResult testOutboundDocumentWithContainer(@RequestBody BasicDocumentInfoDto request) {
        try {
            logger.info("开始测试出库单据容器处理功能");

            // 设置为出库单据
            request.setTransactionType(1); // 出库
            request.setBusinessType(7); // 销售出库

            ResponseResult result = basicDocumentInfoService.addDocumentInfo(request);
            logger.info("测试完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("测试出库单据容器处理失败", e);
            return ResponseResult.getErrorResult("测试失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试用的出库单据数据（自动分配容器）
     * description: 创建一个测试用的出库单据，不指定容器，测试自动分配功能
     * @author: sqpeng
     * @return {@link ResponseResult}
     */
    @PostMapping("/createTestOutboundDocumentAutoAllocation")
    public ResponseResult createTestOutboundDocumentAutoAllocation() {
        try {
            logger.info("创建测试用出库单据（自动分配容器）");

            BasicDocumentInfoDto documentDto = new BasicDocumentInfoDto();
            documentDto.setTransactionType(1); // 出库
            documentDto.setBusinessType(7); // 销售出库
            documentDto.setSupplySalesCode("TEST_CUSTOMER_001");

            // 创建物料明细（不指定容器，测试自动分配）
            List<DocumentDetailDto> details = new ArrayList<>();
            DocumentDetailDto detail1 = new DocumentDetailDto();
            detail1.setMaterialCode("TEST_MATERIAL_001");
            detail1.setQuantity(10);
            // 不设置containerSelections，测试自动分配
            details.add(detail1);

            DocumentDetailDto detail2 = new DocumentDetailDto();
            detail2.setMaterialCode("TEST_MATERIAL_002");
            detail2.setQuantity(5);
            // 不设置containerSelections，测试自动分配
            details.add(detail2);

            documentDto.setDetails(details);

            ResponseResult result = basicDocumentInfoService.addDocumentInfo(documentDto);
            logger.info("测试单据创建完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("创建测试出库单据失败", e);
            return ResponseResult.getErrorResult("创建失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试用的出库单据数据（手动指定容器）
     * description: 创建一个测试用的出库单据，手动指定容器
     * @author: sqpeng
     * @return {@link ResponseResult}
     */
    @PostMapping("/createTestOutboundDocumentManualSelection")
    public ResponseResult createTestOutboundDocumentManualSelection() {
        try {
            logger.info("创建测试用出库单据（手动指定容器）");

            BasicDocumentInfoDto documentDto = new BasicDocumentInfoDto();
            documentDto.setTransactionType(1); // 出库
            documentDto.setBusinessType(7); // 销售出库
            documentDto.setSupplySalesCode("TEST_CUSTOMER_002");

            // 创建物料明细（手动指定容器）
            List<DocumentDetailDto> details = new ArrayList<>();

            // 手动指定容器 - 每个容器批次创建一个明细记录
            // 第一个容器批次
            DocumentDetailDto detail1_container1 = new DocumentDetailDto();
            detail1_container1.setMaterialCode("TEST_MATERIAL_001");
            detail1_container1.setQuantity(5);
            detail1_container1.setContainerCode("TEST_CONTAINER_001");
            detail1_container1.setInventoryId("TEST_INVENTORY_001");
            details.add(detail1_container1);

            // 第二个容器批次（同一物料的不同容器）
            DocumentDetailDto detail1_container2 = new DocumentDetailDto();
            detail1_container2.setMaterialCode("TEST_MATERIAL_001");
            detail1_container2.setQuantity(3);
            detail1_container2.setContainerCode("TEST_CONTAINER_002");
            detail1_container2.setInventoryId("TEST_INVENTORY_002");
            details.add(detail1_container2);

            documentDto.setDetails(details);

            ResponseResult result = basicDocumentInfoService.addDocumentInfo(documentDto);
            logger.info("测试单据创建完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("创建测试出库单据失败", e);
            return ResponseResult.getErrorResult("创建失败: " + e.getMessage());
        }
    }

    /**
     * 测试出库单据更新时的容器处理功能
     * description: 测试单据更新时容器选择的重建逻辑
     * @author: sqpeng
     * @param request 更新请求参数
     * @return {@link ResponseResult}
     */
    @PostMapping("/testUpdateOutboundDocumentWithContainer")
    public ResponseResult testUpdateOutboundDocumentWithContainer(@RequestBody BasicDocumentInfoDto request) {
        try {
            logger.info("开始测试出库单据更新容器处理功能");

            // 确保是出库单据
            request.setTransactionType(1); // 出库
            if (request.getBusinessType() == null) {
                request.setBusinessType(7); // 销售出库
            }

            ResponseResult result = basicDocumentInfoService.updateDocumentInfo(request);
            logger.info("测试完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("测试出库单据更新容器处理失败", e);
            return ResponseResult.getErrorResult("测试失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试用的出库单据更新数据（修改数量和容器）
     * description: 创建一个测试用的单据更新请求，包含数量修改和容器重新分配
     * @author: sqpeng
     * @return {@link ResponseResult}
     */
    @PostMapping("/createTestUpdateOutboundDocument")
    public ResponseResult createTestUpdateOutboundDocument() {
        try {
            logger.info("创建测试用出库单据更新数据");

            BasicDocumentInfoDto documentDto = new BasicDocumentInfoDto();
            // 注意：这里需要设置一个已存在的单据ID
            documentDto.setId("TEST_DOCUMENT_ID"); // 需要替换为实际的单据ID
            documentDto.setTransactionType(1); // 出库
            documentDto.setBusinessType(7); // 销售出库
            documentDto.setSupplySalesCode("TEST_CUSTOMER_UPDATED");
            documentDto.setIsLock(0); // 未锁定

            // 创建更新的明细列表
            List<DocumentDetailDto> details = new ArrayList<>();

            // 修改现有明细的数量和容器
            DocumentDetailDto detail1 = new DocumentDetailDto();
            detail1.setId("EXISTING_DETAIL_ID_1"); // 需要替换为实际的明细ID
            detail1.setMaterialCode("TEST_MATERIAL_001");
            detail1.setQuantity(15); // 修改数量从10到15
            detail1.setContainerCode("TEST_CONTAINER_003"); // 重新指定容器
            detail1.setInventoryId("TEST_INVENTORY_003");
            details.add(detail1);

            // 新增明细
            DocumentDetailDto detail2 = new DocumentDetailDto();
            // 不设置ID，表示新增
            detail2.setMaterialCode("TEST_MATERIAL_003");
            detail2.setQuantity(8);
            // 不指定容器，测试自动分配
            details.add(detail2);

            documentDto.setDetails(details);

            ResponseResult result = basicDocumentInfoService.updateDocumentInfo(documentDto);
            logger.info("测试单据更新完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("创建测试出库单据更新数据失败", e);
            return ResponseResult.getErrorResult("创建失败: " + e.getMessage());
        }
    }


}


