package com.ruoyi.domain.qc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 11:04
 * @Description: 质检详情
 */
@Data
public class QcIpqcDetailInfo {

    private String id;
    /**
     * 质检编码
     */
    private String ipqcCode;

    /**
     * 模板详情ID
     */
    private String templateDetailId;

    /**
     * 检测项编码
     */
    private String itemCode;

    /**
     * 致命缺陷数量
     */
    private Integer crQuantity;

    /**
     * 严重缺陷数量
     */
    private Integer majQuantity;

    /**
     * 轻微缺陷数量
     */
    private Integer minQuantity;

    /**
     * 质检值
     */
    private String qcVal;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 文件名
     */
    private String fileName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
