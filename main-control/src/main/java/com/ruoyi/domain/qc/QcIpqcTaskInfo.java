package com.ruoyi.domain.qc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 9:48
 * @Description: 质检任务
 */
@Data
@TableName("qc_ipqc_task_info")
public class QcIpqcTaskInfo {

    private String id;
    /**
     * 批量质检任务号
     */
    private String batchTask;
    /**
     * 质检编码
     */
    private String ipqcCode;
    /**
     * 质检任务名称
     */
    private String ipqcName;

    /**
     * 检验模板编码
     */
    private String templateCode;

    /**
     * 检验类型
     * 1：来料质检， 4：出库质检
     */
    private Integer qcType;

    /**
     * 来源单据类型
     */
    private String sourceTaskType;

    /**
     * 来源单据
     */
    private String sourceTaskNo;

    /**
     * 供应商\客户编码
     */
    private String companyCode;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 检测数量
     */
    private Integer quantityCheck;

    /**
     * 不合格数
     */
    private Integer quantityUnqualified;

    /**
     * 合格品数量
     */
    private Integer quantityQualified;

    /**
     * 质检结果： 0 待检 1 通过 2 不通过
     */
    private Integer checkResult;

    /**
     * 状态
     * 0 待处理 1 质检完成 2作废 3暂存 4免检
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
