package com.ruoyi.domain.qc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 9:48
 * @Description: 质检任务
 */
@Data
@TableName("qc_ipqc_batch_task")
public class QcIpqcBatchTask {

    private String id;
    /**
     * 批量质检任务号
     */
    private String batchTask;
    /**
     * 质检编码合集
     */
    private String ipqcCodes;
    /**
     * 检测总数量
     */
    private Integer totalCheckNum;

    /**
     * 不合格总数
     */
    private Integer totalUnqualifiedNum;

    /**
     * 合格品数量
     */
    private Integer totalQualifiedNum;

    /**
     * 质检结果： 0 待检 1 通过 2 不通过
     */
    private Integer checkResult;

    /**
     * 状态
     * 0 待处理 1 质检完成 2作废 3暂存 4免检
     */
    private Integer status;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
