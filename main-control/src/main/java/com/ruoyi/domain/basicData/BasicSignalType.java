package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: lhb
 * @CreateDate: 2025/4/19 10:43
 * @Description: 信号类型划分
 */
@Data
@TableName("basic_signal_type")
public class BasicSignalType {

    private String id;

    /**
     * 父名称
     */
    @JsonProperty(value = "parent_signal")
    private String parentSignal;

    /**
     * 信号类型
     */
    @JsonProperty(value = "signal_type")
    private Integer signalType;

    /**
     * 信号类型名称
     */
    @JsonProperty(value = "signal_name")
    private String signalName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remake;

}
