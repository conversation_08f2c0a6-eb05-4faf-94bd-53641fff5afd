package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料仓库基本信息
 */
@Data
@TableName("basic_material_batch_inventory")
public class BasicMaterialBatchInventory {
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 容器编码
     */
    private String containerCode;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料数量
     */
    private Integer materialNum;
    /**
     * 冻结数量
     */
    private Integer freezeNum;
    /**
     * 可用数量
     */
    private Integer availNum;
    /**
     * 入库日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inDate;
    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;
    /**
     * 批次
     */
    private String batch;
    /**
     * 采购单编号
     */
    private String upperIndex;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 是否隔离 0正常，1隔离
     */
    private Integer isIsolation;
    /**
     * 质检次数
     */
    private Integer checkNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 容器位置信息（非数据库字段，用于前端显示）
     * 格式：仓库名-货架名-层级名-位置名
     */
    @TableField(exist = false)
    private String location;

}
