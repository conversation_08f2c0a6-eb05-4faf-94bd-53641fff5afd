package com.ruoyi.domain.basicData;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 库位容器基本信息
 */
@Data
@TableName("basic_warehouse_container")
public class BasicWarehouseContainer {
    /**
     * 主键id
     */
    private String id;
    /**
     * 容器编码
     */
    private String containerCode;
    /**
     * 0：空容器，1：半容器，2：满容器
     */
    private int containerState;
    /**
     * 库位编码
     */
    private String locationCode;
    /**
     * 容器类型，0型材容器，1板材容器，2物料容器
     */
    private Integer containerType;
    /**
     * 容器长
     */
    private Float containerLength;
    /**
     * 容器宽
     */
    private Float containerWidth;
    /**
     * 容器高
     */
    private Float containerHigh;
    /**
     * 状态 0可用，1不可用
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;

}
