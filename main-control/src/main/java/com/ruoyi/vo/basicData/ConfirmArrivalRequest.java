package com.ruoyi.vo.basicData;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 确认到货请求VO
 * 入库场景：确认来料请求
 * 出库场景：确认备货请求
 *
 * <AUTHOR>
 */
@Data
public class ConfirmArrivalRequest {
    
    /**
     * 单据ID
     */
    @NotBlank(message = "单据ID不能为空")
    private String documentId;
    
    /**
     * 到货明细列表（通用）
     * 入库场景：来料明细列表
     * 出库场景：备货明细列表
     */
    @NotEmpty(message = "到货明细列表不能为空")
    @Valid
    private List<MaterialArrivalDto> arrivalList;
}
