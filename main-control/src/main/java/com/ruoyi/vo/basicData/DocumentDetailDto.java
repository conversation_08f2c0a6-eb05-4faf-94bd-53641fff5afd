package com.ruoyi.vo.basicData;

import lombok.Data;

/**
 * 物料明细数据传输对象
 * 支持明细-容器组合模式：一个明细记录对应一个容器批次
 * 如果一个物料需要多个容器，则创建多个明细记录
 */
@Data
public class DocumentDetailDto {

    /**
     * 单据明细ID
     */
    private String id;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 该容器批次的计划数量
     * 入库场景：计划入库数量
     * 出库场景：计划出库数量
     */
    private Integer quantity;

    /**
     * 批次库存ID（仅出库单据使用，可选）
     * 精确指定要出库的批次库存记录
     * 如果不指定，系统将自动分配
     */
    private String inventoryId;

    /**
     * 容器编码（仅出库单据使用，可选）
     * 如果不指定，系统将自动分配
     */
    private String containerCode;
}