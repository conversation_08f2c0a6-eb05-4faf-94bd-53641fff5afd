package com.ruoyi.vo.warehouse;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 物料数量信息
 */
@Data
@HeadRowHeight(20)  // 表头行高
@ContentRowHeight(15)  // 内容行高
public class BasicMaterialNumInfo {

    private String id;

    @ExcelProperty(value = "物料编码", index = 0)
    private String materialCode;

    @ExcelProperty(value = "物料名称", index = 1)
    private String materialName;

    @ExcelProperty(value = "分类编码", index = 2)
    private String classifyCode;

    @ExcelProperty(value = "分类名称", index = 3)
    private String classifyName;

    @ExcelProperty(value = "物料分类", index = 4)
    private String materialSort;

    @ExcelProperty(value = "规格型号", index = 5)
    private String specifications;

    @ExcelProperty(value = "物料单位", index = 6)
    private String produceUnit;

    @ExcelProperty(value = "最低库存", index = 7)
    private Integer minInventory;

    @ExcelProperty(value = "最高库存", index = 8)
    private Integer maxInventory;

    @ExcelProperty(value = "物料数量", index = 9)
    private Integer materialNum;

    @ExcelProperty(value = "冻结数量", index = 10)
    private Integer freezeNum;

    @ExcelProperty(value = "可用数量", index = 11)
    private Integer availNum;

    /**
     * 物料图片
     */
    @ExcelProperty(value = "物料图片", index = 12)
    private String materialImg;
}
