package com.ruoyi.vo.document;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 容器选择DTO
 * 用于出库单据选择容器时的数据传输
 * 
 * <AUTHOR>
 */
@Data
public class ContainerSelectionDto {
    
    /**
     * 单据明细ID
     */
    @NotBlank(message = "单据明细ID不能为空")
    private String detailId;
    
    /**
     * 容器编码
     */
    @NotBlank(message = "容器编码不能为空")
    private String containerCode;

    /**
     * 出库数量
     */
    @NotNull(message = "出库数量不能为空")
    @Positive(message = "出库数量必须大于0")
    private Integer quantity;

    /**
     * 批次库存ID - 精确指定要出库的批次库存记录
     * 通过此ID可以获取到物料编码、批次号、生产日期等所有批次信息
     */
    @NotBlank(message = "批次库存ID不能为空")
    private String inventoryId;
}
