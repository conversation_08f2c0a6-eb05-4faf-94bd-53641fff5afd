package com.ruoyi.vo.document;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 选择容器请求DTO
 * 用于出库单据选择容器的请求参数
 * 
 * <AUTHOR>
 */
@Data
public class SelectContainerRequest {
    
    /**
     * 单据ID
     */
    @NotBlank(message = "单据ID不能为空")
    private String documentId;
    
    /**
     * 容器选择列表
     */
    @NotEmpty(message = "容器选择列表不能为空")
    @Valid
    private List<ContainerSelectionDto> containerSelections;
}
