package com.ruoyi.vo.document;

import lombok.Data;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.vo.warehouse.BasicMaterialNumInfo;

import java.util.List;

/**
 * 物料及其可用容器信息DTO
 * 用于出库单据选择物料时，同时返回物料基本信息和可用容器列表
 * 
 * <AUTHOR>
 */
@Data
public class MaterialWithContainersDto {
    
    /**
     * 物料基本信息
     */
    private BasicMaterialNumInfo materialInfo;
    
    /**
     * 该物料的可用容器列表
     */
    private List<BasicMaterialBatchInventory> availableContainers;
}
