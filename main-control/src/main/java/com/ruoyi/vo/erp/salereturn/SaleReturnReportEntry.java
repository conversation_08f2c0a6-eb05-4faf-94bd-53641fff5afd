package com.ruoyi.vo.erp.salereturn;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import com.ruoyi.vo.erp.common.FNumberBig;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售退货单明细上报实体
 * <AUTHOR>
 */
@Data
public class SaleReturnReportEntry {

    /**
     * 实体主键
     */
    @JsonProperty("FENTRYID")
    private Integer fEntryID;

    /**
     * 产品类型
     */
    @JsonProperty("FRowType")
    private String fRowType;

    /**
     * 物料编码
     */
    @JsonProperty("FMaterialId")
    private FNumber fMaterialId;

    /**
     * 物料名称
     */
    @JsonProperty("FMaterialName")
    private String fMaterialName;

    /**
     * 规格型号
     */
    @JsonProperty("FMaterialModel")
    private String fMaterialModel;

    /**
     * 物料类别
     */
    @JsonProperty("FMaterialType")
    private FNumber fMaterialType;

    /**
     * 库存单位
     */
    @JsonProperty("FUnitID")
    private FNumber fUnitID;

    /**
     * 应退数量
     */
    @JsonProperty("FMustqty")
    private BigDecimal fMustQty;

    /**
     * 实退数量
     */
    @JsonProperty("FRealQty")
    private BigDecimal fRealQty;

    /**
     * 仓库
     */
    @JsonProperty("FStockId")
    private FNumber fStockId;

    /**
     * 库存状态
     */
    @JsonProperty("FStockstatusId")
    private FNumber fStockStatusId;

    /**
     * 货主类型
     */
    @JsonProperty("FOwnerTypeId")
    private String fOwnerTypeId;

    /**
     * 货主
     */
    @JsonProperty("FOwnerId")
    private FNumber fOwnerId;

    /**
     * 保管者类型
     */
    @JsonProperty("FKeeperTypeId")
    private String fKeeperTypeId;

    /**
     * 保管者
     */
    @JsonProperty("FKeeperId")
    private FNumber fKeeperId;

    /**
     * BOM版本
     */
    @JsonProperty("FBOMId")
    private FNumber fBomId;

    /**
     * 生产日期
     */
    @JsonProperty("FProduceDate")
    private String fProduceDate;

    /**
     * 有效期至
     */
    @JsonProperty("FExpiryDate")
    private String fExpiryDate;

    /**
     * 基本单位
     */
    @JsonProperty("FBaseunitId")
    private FNumber fBaseUnitId;

    /**
     * 库存基本数量
     */
    @JsonProperty("FBaseunitQty")
    private BigDecimal fBaseUnitQty;

    /**
     * 库存辅单位
     */
    @JsonProperty("FAuxUnitId")
    private FNumber fAuxUnitId;

    /**
     * 库存辅单位数量
     */
    @JsonProperty("FAuxUnitQty")
    private BigDecimal fAuxUnitQty;

    /**
     * 成本价（本位币）
     */
    @JsonProperty("FCostPrice")
    private BigDecimal fCostPrice;

    /**
     * 总成本
     */
    @JsonProperty("FEntryCostAmount")
    private BigDecimal fEntryCostAmount;

    /**
     * 订单单号
     */
    @JsonProperty("FOrderNo")
    private String fOrderNo;

    /**
     * 备注
     */
    @JsonProperty("FNote")
    private String fNote;

    /**
     * 库存更新标示
     */
    @JsonProperty("FStockFlag")
    private Boolean fStockFlag;

    /**
     * 源单类型
     */
    @JsonProperty("FSrcBillTypeID")
    private String fSrcBillTypeID;

    /**
     * 仓位
     */
    @JsonProperty("FStocklocId")
    private FNumber fStockLocId;

    /**
     * 客户物料编码
     */
    @JsonProperty("FMapId")
    private FNumber fMapId;

    /**
     * 客户物料名称
     */
    @JsonProperty("FMapName")
    private String fMapName;

    /**
     * 批号
     */
    @JsonProperty("FLot")
    private FNumber fLot;

    /**
     * 退货类型
     */
    @JsonProperty("FReturnType")
    private FNumber fReturnType;

    /**
     * 计价单位
     */
    @JsonProperty("FPriceUnitId")
    private FNumber fPriceUnitId;

    /**
     * 计价数量
     */
    @JsonProperty("FPriceUnitQty")
    private BigDecimal fPriceUnitQty;

    /**
     * 单价
     */
    @JsonProperty("FPrice")
    private BigDecimal fPrice;

    /**
     * 含税单价
     */
    @JsonProperty("FTaxPrice")
    private BigDecimal fTaxPrice;

    /**
     * 税率%
     */
    @JsonProperty("FEntryTaxRate")
    private BigDecimal fEntryTaxRate;

    /**
     * 金额
     */
    @JsonProperty("FAmount")
    private BigDecimal fAmount;

    /**
     * 税额
     */
    @JsonProperty("FEntryTaxAmount")
    private BigDecimal fEntryTaxAmount;

    /**
     * 价税合计
     */
    @JsonProperty("FAllAmount")
    private BigDecimal fAllAmount;

    /**
     * 退货日期
     */
    @JsonProperty("FDeliveryDate")
    private String fDeliveryDate;

    /**
     * 销售单位
     */
    @JsonProperty("FSalUnitID")
    private FNumber fSalUnitID;

    /**
     * 销售数量
     */
    @JsonProperty("FSalUnitQty")
    private BigDecimal fSalUnitQty;

    /**
     * 销售基本数量
     */
    @JsonProperty("FSalBaseQty")
    private BigDecimal fSalBaseQty;

    /**
     * 计价基本数量
     */
    @JsonProperty("FPriceBaseQty")
    private BigDecimal fPriceBaseQty;

    /**
     * 未关联应收数量（计价单位）
     */
    @JsonProperty("FARNOTJOINQTY")
    private BigDecimal fArNotJoinQty;

    /**
     * 物料（销售组织）
     */
    @JsonProperty("FMaterialID_Sal")
    private FNumberBig fMaterialID_Sal;

    /**
     * 入库日期
     */
    @JsonProperty("FINSTOCKDATE")
    private String fInStockDate;

    /**
     * 关联关系表
     */
    @JsonProperty("FEntity_Link")
    private List<FSaleReturnEntryLink> fEntityLink;
}
