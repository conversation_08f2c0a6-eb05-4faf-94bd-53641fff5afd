package com.ruoyi.vo.erp.purchasereturn;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import com.ruoyi.vo.erp.common.FNumberBig;
import lombok.Data;

import java.util.List;

/**
 * 采购退料单上报实体
 * <AUTHOR>
 */
@Data
public class PurchaseReturnReport {

    /**
     * 单据编号
     */
    @JsonProperty("FBillNo")
    private String fBillNo;

    /**
     * 单据类型
     */
    @JsonProperty("FBillTypeID")
    private FNumberBig fBillTypeID;

    /**
     * 业务类型
     */
    @JsonProperty("FBusinessType")
    private String fBusinessType;

    /**
     * 退料日期
     */
    @JsonProperty("FDate")
    private String fDate;

    /**
     * 退料类型
     */
    @JsonProperty("FMRTYPE")
    private String fMrType;

    /**
     * 退料方式
     */
    @JsonProperty("FMRMODE")
    private String fMrMode;

    /**
     * 退料组织
     */
    @JsonProperty("FStockOrgId")
    private FNumber fStockOrgId;

    /**
     * 需求组织
     */
    @JsonProperty("FRequireOrgId")
    private FNumber fRequireOrgId;

    /**
     * 采购组织
     */
    @JsonProperty("FPurchaseOrgId")
    private FNumber fPurchaseOrgId;

    /**
     * 供应商
     */
    @JsonProperty("FSupplierID")
    private FNumber fSupplierID;

    /**
     * 接收方
     */
    @JsonProperty("FACCEPTORID")
    private FNumber fAcceptorId;

    /**
     * 结算方
     */
    @JsonProperty("FSettleId")
    private FNumber fSettleId;

    /**
     * 收款方
     */
    @JsonProperty("FCHARGEID")
    private FNumber fChargeId;

    /**
     * 货主类型
     */
    @JsonProperty("FOwnerTypeIdHead")
    private String fOwnerTypeIdHead;

    /**
     * 货主
     */
    @JsonProperty("FOwnerIdHead")
    private FNumber fOwnerIdHead;

    /**
     * 验收方式
     */
    @JsonProperty("FACCTYPE")
    private String fAccType;

    /**
     * 退料部门
     */
    @JsonProperty("FMRDeptId")
    private FNumber fMrDeptId;

    /**
     * 采购部门
     */
    @JsonProperty("FPURCHASEDEPTID")
    private FNumber fPurchaseDeptId;

    /**
     * 采购员
     */
    @JsonProperty("FPURCHASERID")
    private FNumber fPurchaserId;

    /**
     * 采购组
     */
    @JsonProperty("FPURCHASERGROUPID")
    private FNumber fPurchaserGroupId;

    /**
     * 仓管员
     */
    @JsonProperty("FSTOCKERID")
    private FNumber fStockerId;

    /**
     * 库存组
     */
    @JsonProperty("FSTOCKERGROUPID")
    private FNumber fStockerGroupId;

    /**
     * 承运商
     */
    @JsonProperty("FCarrierID")
    private FNumber fCarrierId;

    /**
     * 送货单号
     */
    @JsonProperty("FDELIVERYNO")
    private String fDeliveryNo;

    /**
     * 提货单号
     */
    @JsonProperty("FTAKEDELIVERYNO")
    private String fTakeDeliveryNo;

    /**
     * 运输单号
     */
    @JsonProperty("FCARRYNO")
    private String fCarryNo;

    /**
     * 退料原因
     */
    @JsonProperty("FMRREASON")
    private String fMrReason;

    /**
     * 补料方式
     */
    @JsonProperty("FREPLENISHMODE")
    private String fReplenishMode;

    /**
     * 备注
     */
    @JsonProperty("FDESCRIPTION")
    private String fDescription;

    /**
     * 跨组织业务类型
     */
    @JsonProperty("FTransferBizType")
    private String fTransferBizType;

    /**
     * 对应组织
     */
    @JsonProperty("FCorrespondOrgId")
    private FNumber fCorrespondOrgId;

    /**
     * 序列号上传
     */
    @JsonProperty("FScanBox")
    private String fScanBox;

    /**
     * 项目（基）
     */
    @JsonProperty("F_PCZO_Base")
    private FNumberBig fPczoBase;

    /**
     * 项目经理
     */
    @JsonProperty("F_PCZO_Base1")
    private FNumber fPczoBase1;

    /**
     * 财务信息
     */
    @JsonProperty("FPURMRBFIN")
    private PurchaseReturnReportFin fPurMrbFin;

    /**
     * 明细信息
     */
    @JsonProperty("FPURMRBENTRY")
    private List<PurchaseReturnReportEntry> fPurMrbEntry;
}
