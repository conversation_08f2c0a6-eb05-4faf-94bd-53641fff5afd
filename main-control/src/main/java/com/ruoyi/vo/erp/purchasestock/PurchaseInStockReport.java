package com.ruoyi.vo.erp.purchasestock;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import com.ruoyi.vo.erp.common.FNumberBig;
import lombok.Data;

import java.util.List;

/**
 * 采购入库单上报实体
 * <AUTHOR>
 */
@Data
public class PurchaseInStockReport {

    /**
     * 单据编号
     */
    @JsonProperty("FBillNo")
    private String fBillNo;

    /**
     * 单据类型
     */
    @JsonProperty("FBillTypeID")
    private FNumberBig fBillTypeID;

    /**
     * 业务类型
     */
    @JsonProperty("FBusinessType")
    private String fBusinessType;

    /**
     * 入库日期
     */
    @JsonProperty("FDate")
    private String fDate;

    /**
     * 收料组织
     */
    @JsonProperty("FStockOrgId")
    private FNumber fStockOrgId;

    /**
     * 需求组织
     */
    @JsonProperty("FDemandOrgId")
    private FNumber fDemandOrgId;

    /**
     * 采购组织
     */
    @JsonProperty("FPurchaseOrgId")
    private FNumber fPurchaseOrgId;

    /**
     * 采购部门
     */
    @JsonProperty("FPurchaseDeptId")
    private FNumber fPurchaseDeptId;

    /**
     * 供应商
     */
    @JsonProperty("FSupplierId")
    private FNumber fSupplierId;

    /**
     * 供货方
     */
    @JsonProperty("FSupplyId")
    private FNumber fSupplyId;

    /**
     * 结算方
     */
    @JsonProperty("FSettleId")
    private FNumber fSettleId;

    /**
     * 收款方
     */
    @JsonProperty("FChargeId")
    private FNumber fChargeId;

    /**
     * 货主类型(表头)
     */
    @JsonProperty("FOwnerTypeIdHead")
    private String fOwnerTypeIdHead;

    /**
     * 货主(表头)
     */
    @JsonProperty("FOwnerIdHead")
    private FNumber fOwnerIdHead;

    /**
     * 拆单类型
     */
    @JsonProperty("FSplitBillType")
    private String fSplitBillType;

    /**
     * 库存组
     */
    @JsonProperty("FStockerGroupId")
    private FNumber fStockerGroupId;

    /**
     * 收料部门
     */
    @JsonProperty("FStockDeptId")
    private FNumber fStockDeptId;

    /**
     * 仓管员
     */
    @JsonProperty("FStockerId")
    private FNumber fStockerId;

    /**
     * 采购组
     */
    @JsonProperty("FPurchaserGroupId")
    private FNumber fPurchaserGroupId;

    /**
     * 采购员
     */
    @JsonProperty("FPurchaserId")
    private FNumber fPurchaserId;

    /**
     * 跨组织业务类型
     */
    @JsonProperty("FTransferBizType")
    private String fTransferBizType;

    /**
     * 对应组织
     */
    @JsonProperty("FCorrespondOrgId")
    private FNumber fCorrespondOrgId;

    /**
     * 序列号上传
     */
    @JsonProperty("FScanBox")
    private String fScanBox;

    /**
     * 项目（基）
     */
    @JsonProperty("F_PCZO_Base")
    private FNumberBig fPczoBase;

    /**
     * 项目经理
     */
    @JsonProperty("F_PCZO_Base1")
    private FNumber fPczoBase1;

    /**
     * 财务信息
     */
    @JsonProperty("FInStockFin")
    private PurchaseInStockReportFin fInStockFin;

    /**
     * 明细信息
     */
    @JsonProperty("FInStockEntry")
    private List<PurchaseInStockReportEntry> fInStockEntry;
}
