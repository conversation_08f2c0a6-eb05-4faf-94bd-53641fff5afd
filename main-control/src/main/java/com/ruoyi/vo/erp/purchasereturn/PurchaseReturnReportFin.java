package com.ruoyi.vo.erp.purchasereturn;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购退料单财务信息实体
 * <AUTHOR>
 */
@Data
public class PurchaseReturnReportFin {

    /**
     * 结算组织
     */
    @JsonProperty("FSettleOrgId")
    private FNumber fSettleOrgId;

    /**
     * 结算币别
     */
    @JsonProperty("FSettleCurrId")
    private FNumber fSettleCurrId;

    /**
     * 含税
     */
    @JsonProperty("FIsIncludedTax")
    private Boolean fIsIncludedTax;

    /**
     * 定价时点
     */
    @JsonProperty("FPRICETIMEPOINT")
    private String fPriceTimePoint;

    /**
     * 本位币
     */
    @JsonProperty("FLOCALCURRID")
    private FNumber fLocalCurrId;

    /**
     * 汇率类型
     */
    @JsonProperty("FEXCHANGETYPEID")
    private FNumber fExchangeTypeId;

    /**
     * 汇率
     */
    @JsonProperty("FEXCHANGERATE")
    private BigDecimal fExchangeRate;

    /**
     * 价外税
     */
    @JsonProperty("FISPRICEEXCLUDETAX")
    private Boolean fIsPriceExcludeTax;

    /**
     * 核算反写汇率
     */
    @JsonProperty("FHSExchangeRate")
    private BigDecimal fHSExchangeRate;

    /**
     * 付款组织
     */
    @JsonProperty("FPayOrgId")
    private FNumber fPayOrgId;

    /**
     * 结算方式
     */
    @JsonProperty("FSETTLETYPEID")
    private FNumber fSettleTypeId;

    /**
     * 付款条件
     */
    @JsonProperty("FPAYCONDITIONID")
    private FNumber fPayConditionId;

    /**
     * 税额
     */
    @JsonProperty("FBILLTAXAMOUNT")
    private BigDecimal fBillTaxAmount;

    /**
     * 总成本
     */
    @JsonProperty("FBILLCOSTAMOUNT")
    private BigDecimal fBillCostAmount;

    /**
     * 退料金额
     */
    @JsonProperty("FBILLALLAMOUNT")
    private BigDecimal fBillAllAmount;

    /**
     * 价目表
     */
    @JsonProperty("FPRICELISTID")
    private FNumber fPriceListId;

    /**
     * 折扣表
     */
    @JsonProperty("FDISCOUNTLISTID")
    private FNumber fDiscountListId;

    /**
     * 价税合计(本位币)
     */
    @JsonProperty("FBILLALLAMOUNT_LC")
    private BigDecimal fBillAllAmount_LC;

    /**
     * 税额(本位币)
     */
    @JsonProperty("FBILLTAXAMOUNT_LC")
    private BigDecimal fBillTaxAmount_LC;

    /**
     * 总成本(本位币)
     */
    @JsonProperty("FBILLCOSTAMOUNT_LC")
    private BigDecimal fBillCostAmount_LC;

    /**
     * 金额
     */
    @JsonProperty("FBillAmount")
    private BigDecimal fBillAmount;

    /**
     * 金额(本位币)
     */
    @JsonProperty("FBillAmount_LC")
    private BigDecimal fBillAmount_LC;

    /**
     * 跨组织结算生成
     */
    @JsonProperty("FISGENFORIOS")
    private Boolean fIsGenForIOS;

    /**
     * 货主客户
     */
    @JsonProperty("FOwnerCustomerID")
    private FNumber fOwnerCustomerID;

    /**
     * 结算组织供应商
     */
    @JsonProperty("FSettleSupplierID")
    private FNumber fSettleSupplierID;

    /**
     * 先到票后入库
     */
    @JsonProperty("FISINVOICEARLIER")
    private Boolean fIsInvoiceEarlier;

    /**
     * 第三方来源
     */
    @JsonProperty("FTHIRDSRCTYPE")
    private String fThirdSrcType;

    /**
     * 第三方单据ID
     */
    @JsonProperty("FTHIRDBILLID")
    private String fThirdBillId;

    /**
     * 第三方单据编号
     */
    @JsonProperty("FTHIRDBILLNO")
    private String fThirdBillNo;
}
