package com.ruoyi.vo.erp.purchasestock;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购入库单财务信息实体
 * <AUTHOR>
 */
@Data
public class PurchaseInStockReportFin {

    /**
     * 结算组织
     */
    @JsonProperty("FSettleOrgId")
    private FNumber fSettleOrgId;

    /**
     * 结算币别
     */
    @JsonProperty("FSettleCurrId")
    private FNumber fSettleCurrId;

    /**
     * 含税
     */
    @JsonProperty("FIsIncludedTax")
    private Boolean fIsIncludedTax;

    /**
     * 定价时点
     */
    @JsonProperty("FPriceTimePoint")
    private String fPriceTimePoint;

    /**
     * 本位币
     */
    @JsonProperty("FLocalCurrId")
    private FNumber fLocalCurrId;

    /**
     * 汇率类型
     */
    @JsonProperty("FExchangeTypeId")
    private FNumber fExchangeTypeId;

    /**
     * 汇率
     */
    @JsonProperty("FExchangeRate")
    private BigDecimal fExchangeRate;

    /**
     * 价外税
     */
    @JsonProperty("FISPRICEEXCLUDETAX")
    private Boolean fIsPriceExcludeTax;

    /**
     * 核算反写汇率
     */
    @JsonProperty("FHSExchangeRate")
    private BigDecimal fHSExchangeRate;

    /**
     * 付款组织
     */
    @JsonProperty("FPayOrgId")
    private FNumber fPayOrgId;

    /**
     * 整单费用
     */
    @JsonProperty("FBillCost")
    private BigDecimal fBillCost;

    /**
     * 税额
     */
    @JsonProperty("FBillTaxAmount")
    private BigDecimal fBillTaxAmount;

    /**
     * 总成本
     */
    @JsonProperty("FBillCostAmount")
    private BigDecimal fBillCostAmount;

    /**
     * 价税合计
     */
    @JsonProperty("FBillAllAmount")
    private BigDecimal fBillAllAmount;

    /**
     * 结算方式
     */
    @JsonProperty("FSettleTypeId")
    private FNumber fSettleTypeId;

    /**
     * 付款条件
     */
    @JsonProperty("FPayConditionId")
    private FNumber fPayConditionId;

    /**
     * 税额(本位币)
     */
    @JsonProperty("FBillTaxAmount_LC")
    private BigDecimal fBillTaxAmountLC;

    /**
     * 总成本(本位币)
     */
    @JsonProperty("FBilCostAmount_LC")
    private BigDecimal fBilCostAmountLC;

    /**
     * 价税合计(本位币)
     */
    @JsonProperty("FBillAllAmount_LC")
    private BigDecimal fBillAllAmountLC;

    /**
     * 折扣表
     */
    @JsonProperty("FDiscountListId")
    private FNumber fDiscountListId;

    /**
     * 价目表
     */
    @JsonProperty("FPriceListId")
    private FNumber fPriceListId;

    /**
     * 金额(本位币)
     */
    @JsonProperty("FBillAmount_LC")
    private BigDecimal fBillAmountLC;

    /**
     * 金额
     */
    @JsonProperty("FBillAmount")
    private BigDecimal fBillAmount;

    /**
     * 货主客户
     */
    @JsonProperty("FOwnerCustomerID")
    private FNumber fOwnerCustomerID;

    /**
     * 结算组织供应商
     */
    @JsonProperty("FSettleSupplierID")
    private FNumber fSettleSupplierID;

    /**
     * 跨组织结算生成
     */
    @JsonProperty("FISGENFORIOS")
    private Boolean fIsGenForIOS;

    /**
     * 先到票后入库
     */
    @JsonProperty("FISINVOICEARLIER")
    private Boolean fIsInvoiceArlier;

    /**
     * 第三方来源
     */
    @JsonProperty("FTHIRDSRCTYPE")
    private String fThirdSrcType;

    /**
     * 第三方单据ID
     */
    @JsonProperty("FTHIRDBILLID")
    private String fThirdBillId;

    /**
     * 第三方单据编号
     */
    @JsonProperty("FTHIRDBILLNO")
    private String fThirdBillNo;

    /**
     * 整单折扣额
     */
    @JsonProperty("FAllDisCount")
    private BigDecimal fAllDisCount;
}
