package com.ruoyi.vo.erp.otherstock;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import com.ruoyi.vo.erp.common.FNumberBig;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 直接调拨单明细上报实体
 * <AUTHOR>
 */
@Data
public class TransferBillReportEntry {

    /**
     * 实体主键
     */
    @JsonProperty("FEntryID")
    private Integer fEntryID;

    /**
     * 行类型
     */
    @JsonProperty("FRowType")
    private String fRowType;

    /**
     * 物料编码
     */
    @JsonProperty("FMaterialId")
    private FNumber fMaterialId;

    /**
     * 单位
     */
    @JsonProperty("FUnitID")
    private FNumber fUnitId;

    /**
     * 基本单位
     */
    @JsonProperty("FBaseUnitId")
    private FNumber fBaseUnitId;

    /**
     * 调拨数量
     */
    @JsonProperty("FQty")
    private BigDecimal fQty;

    /**
     * 基本单位数量
     */
    @JsonProperty("FBaseQty")
    private BigDecimal fBaseQty;

    /**
     * 销售数量
     */
    @JsonProperty("FSaleQty")
    private BigDecimal fSaleQty;

    /**
     * 销售基本单位数量
     */
    @JsonProperty("FSalBaseQty")
    private BigDecimal fSalBaseQty;

    /**
     * 计价数量
     */
    @JsonProperty("FPriceQty")
    private BigDecimal fPriceQty;

    /**
     * 计价基本单位数量
     */
    @JsonProperty("FPriceBaseQty")
    private BigDecimal fPriceBaseQty;

    /**
     * 调出批号
     */
    @JsonProperty("FLot")
    private FNumber fLot;

    /**
     * 调入批号
     */
    @JsonProperty("FDestLot")
    private FNumber fDestLot;

    /**
     * 调出仓库
     */
    @JsonProperty("FSrcStockId")
    private FNumber fSrcStockId;

    /**
     * 调入仓库
     */
    @JsonProperty("FDestStockId")
    private FNumber fDestStockId;

    /**
     * 调出库存状态
     */
    @JsonProperty("FSrcStockStatusId")
    private FNumber fSrcStockStatusId;

    /**
     * 调入库存状态
     */
    @JsonProperty("FDestStockStatusId")
    private FNumber fDestStockStatusId;

    /**
     * 调入仓位
     */
    @JsonProperty("FDestStockLocId")
    private FDestStockLocId fDestStockLocId;

    /**
     * 业务日期
     */
    @JsonProperty("FBusinessDate")
    private String fBusinessDate;

    /**
     * 调出货主类型
     */
    @JsonProperty("FOwnerTypeOutId")
    private String fOwnerTypeOutId;

    /**
     * 调出货主
     */
    @JsonProperty("FOwnerOutId")
    private FNumber fOwnerOutId;

    /**
     * 调入货主类型
     */
    @JsonProperty("FOwnerTypeId")
    private String fOwnerTypeId;

    /**
     * 调入货主
     */
    @JsonProperty("FOwnerId")
    private FNumber fOwnerId;

    /**
     * 保管者类型
     */
    @JsonProperty("FKeeperTypeId")
    private String fKeeperTypeId;

    /**
     * 保管者
     */
    @JsonProperty("FKeeperId")
    private FNumber fKeeperId;

    /**
     * 调出保管者类型
     */
    @JsonProperty("FKeeperTypeOutId")
    private String fKeeperTypeOutId;

    /**
     * 调出保管者
     */
    @JsonProperty("FKeeperOutId")
    private FNumber fKeeperOutId;

    /**
     * 目标物料
     */
    @JsonProperty("FDestMaterialId")
    private FNumberBig fDestMaterialId;

    /**
     * 销售单位
     */
    @JsonProperty("FSaleUnitId")
    private FNumber fSaleUnitId;

    /**
     * 计价单位
     */
    @JsonProperty("FPriceUnitID")
    private FNumber fPriceUnitId;

    /**
     * 调入仓位
     */
    @JsonProperty("FDestStockLocId")
    private FDestStockLocId fDestStockLoc;

    /**
     * 关联关系表
     */
    @JsonProperty("FBillEntry_Link")
    private List<FBillEntryLink> fBillEntryLink;

    /**
     * 调入仓位信息
     */
    @Data
    public static class FDestStockLocId {
        /**
         * 区域
         */
        @JsonProperty("FDESTSTOCKLOCID__FF100012")
        private FNumber fDestStockLocIdFF100012;

        /**
         * 位置
         */
        @JsonProperty("FDESTSTOCKLOCID__FF100013")
        private FNumber fDestStockLocIdFF100013;
    }

    /**
     * 关联关系表
     */
    @Data
    public static class FBillEntryLink {
        /**
         * 转换规则
         */
        @JsonProperty("FBillEntry_Link_FRuleId")
        private String fBillEntryLinkFRuleId;

        /**
         * 源单表
         */
        @JsonProperty("FBillEntry_Link_FSTableName")
        private String fBillEntryLinkFSTableName;

        /**
         * 源单内码
         */
        @JsonProperty("FBillEntry_Link_FSBillId")
        private String fBillEntryLinkFSBillId;

        /**
         * 源单分录内码
         */
        @JsonProperty("FBillEntry_Link_FSId")
        private String fBillEntryLinkFSId;

        /**
         * 原始携带量
         */
        @JsonProperty("FBillEntry_Link_FBaseQtyOld")
        private Double fBillEntryLinkFBaseQtyOld;

        /**
         * 修改携带量
         */
        @JsonProperty("FBillEntry_Link_FBaseQty")
        private Double fBillEntryLinkFBaseQty;

        /**
         * 原始携带量（销售基本）
         */
        @JsonProperty("FBillEntry_Link_FSalBaseQtyOld")
        private Double fBillEntryLinkFSalBaseQtyOld;

        /**
         * 修改携带量（销售基本）
         */
        @JsonProperty("FBillEntry_Link_FSalBaseQty")
        private Double fBillEntryLinkFSalBaseQty;

        /**
         * 原始携带量（计价基本）
         */
        @JsonProperty("FBillEntry_Link_FPriceBaseQtyOld")
        private Double fBillEntryLinkFPriceBaseQtyOld;

        /**
         * 修改携带量（计价基本）
         */
        @JsonProperty("FBillEntry_Link_FPriceBaseQty")
        private Double fBillEntryLinkFPriceBaseQty;
    }

    /**
     * FName类型字段
     */
    @Data
    public static class FName {
        @JsonProperty("FName")
        private String fName;

        public static FName of(String name) {
            FName fn = new FName();
            fn.setFName(name);
            return fn;
        }
    }
}