package com.ruoyi.vo.erp.purchasereturn;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购退料单关联关系实体
 * <AUTHOR>
 */
@Data
public class FPurMrbEntryLink {

    /**
     * 业务流程图
     */
    @JsonProperty("FPURMRBENTRY_Link_FFlowId")
    private FNumber fPurMrbEntryLinkFFlowId;

    /**
     * 推进路线
     */
    @JsonProperty("FPURMRBENTRY_Link_FFlowLineId")
    private FNumber fPurMrbEntryLinkFFlowLineId;

    /**
     * 转换规则
     */
    @JsonProperty("FPURMRBENTRY_Link_FRuleId")
    private String fPurMrbEntryLinkFRuleId;

    /**
     * 源单表内码
     */
    @JsonProperty("FPURMRBENTRY_Link_FSTableId")
    private String fPurMrbEntryLinkFSTableId;

    /**
     * 源单表
     */
    @JsonProperty("FPURMRBENTRY_Link_FSTableName")
    private String fPurMrbEntryLinkFSTableName;

    /**
     * 源单内码
     */
    @JsonProperty("FPURMRBENTRY_Link_FSBillId")
    private String fPurMrbEntryLinkFSBillId;

    /**
     * 源单分录内码
     */
    @JsonProperty("FPURMRBENTRY_Link_FSId")
    private String fPurMrbEntryLinkFSId;

    /**
     * 原始携带量
     */
    @JsonProperty("FPURMRBENTRY_Link_FCarryBaseQtyOld")
    private BigDecimal fPurMrbEntryLinkFCarryBaseQtyOld;

    /**
     * 修改携带量
     */
    @JsonProperty("FPURMRBENTRY_Link_FCarryBaseQty")
    private BigDecimal fPurMrbEntryLinkFCarryBaseQty;

    /**
     * 原始携带量
     */
    @JsonProperty("FPURMRBENTRY_Link_FBASEUNITQTYOld")
    private BigDecimal fPurMrbEntryLinkFBaseUnitQtyOld;

    /**
     * 修改携带量
     */
    @JsonProperty("FPURMRBENTRY_Link_FBASEUNITQTY")
    private BigDecimal fPurMrbEntryLinkFBaseUnitQty;

    /**
     * 迁移图
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk1TrackerId")
    private FNumber fPurMrbEntryLinkFLnk1TrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk1SState")
    private String fPurMrbEntryLinkFLnk1SState;

    /**
     * 数量FLnk1
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk1Amount")
    private BigDecimal fPurMrbEntryLinkFLnk1Amount;

    /**
     * 迁移图
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnkTrackerId")
    private FNumber fPurMrbEntryLinkFLnkTrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnkSState")
    private String fPurMrbEntryLinkFLnkSState;

    /**
     * 数量FLnk
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnkAmount")
    private BigDecimal fPurMrbEntryLinkFLnkAmount;

    /**
     * 迁移图
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk2TrackerId")
    private FNumber fPurMrbEntryLinkFLnk2TrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk2SState")
    private String fPurMrbEntryLinkFLnk2SState;

    /**
     * 数量FLnk2
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk2Amount")
    private BigDecimal fPurMrbEntryLinkFLnk2Amount;

    /**
     * 迁移图
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk3TrackerId")
    private FNumber fPurMrbEntryLinkFLnk3TrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk3SState")
    private String fPurMrbEntryLinkFLnk3SState;

    /**
     * 数量FLnk3
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk3Amount")
    private BigDecimal fPurMrbEntryLinkFLnk3Amount;

    /**
     * 迁移图
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk4TrackerId")
    private FNumber fPurMrbEntryLinkFLnk4TrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk4SState")
    private String fPurMrbEntryLinkFLnk4SState;

    /**
     * 数量FLnk4
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk4Amount")
    private BigDecimal fPurMrbEntryLinkFLnk4Amount;

    /**
     * 迁移图
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk5TrackerId")
    private FNumber fPurMrbEntryLinkFLnk5TrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk5SState")
    private String fPurMrbEntryLinkFLnk5SState;

    /**
     * 数量FLnk5
     */
    @JsonProperty("FPURMRBENTRY_Link_FLnk5Amount")
    private BigDecimal fPurMrbEntryLinkFLnk5Amount;
}
