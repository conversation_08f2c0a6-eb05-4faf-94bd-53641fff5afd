package com.ruoyi.vo.erp.salereturn;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售退货单财务信息实体
 * <AUTHOR>
 */
@Data
public class SaleReturnReportFin {

    /**
     * 结算方式
     */
    @JsonProperty("FSettleTypeId")
    private FNumber fSettleTypeId;

    /**
     * 收款条件
     */
    @JsonProperty("FChageCondition")
    private FNumber fChageCondition;

    /**
     * 结算组织
     */
    @JsonProperty("FSettleOrgId")
    private FNumber fSettleOrgId;

    /**
     * 结算币别
     */
    @JsonProperty("FSettleCurrId")
    private FNumber fSettleCurrId;

    /**
     * 本位币
     */
    @JsonProperty("FLocalCurrId")
    private FNumber fLocalCurrId;

    /**
     * 汇率类型
     */
    @JsonProperty("FExchangeTypeId")
    private FNumber fExchangeTypeId;

    /**
     * 汇率
     */
    @JsonProperty("FExchangeRate")
    private BigDecimal fExchangeRate;

    /**
     * 总成本
     */
    @JsonProperty("FBillCostAmount")
    private BigDecimal fBillCostAmount;

    /**
     * 总成本（本位币）
     */
    @JsonProperty("FBillCostAmount_LC")
    private BigDecimal fBillCostAmount_LC;

    /**
     * 汇率精度
     */
    @JsonProperty("FPrecision")
    private Integer fPrecision;

    /**
     * 价税合计（本位币）
     */
    @JsonProperty("FBillAllAmount_LC")
    private BigDecimal fBillAllAmount_LC;

    /**
     * 金额（本位币）
     */
    @JsonProperty("FBillAmount_LC")
    private BigDecimal fBillAmount_LC;

    /**
     * 税额（本位币）
     */
    @JsonProperty("FBillTaxAmount_LC")
    private BigDecimal fBillTaxAmount_LC;

    /**
     * 价税合计
     */
    @JsonProperty("FBillAllAmount")
    private BigDecimal fBillAllAmount;

    /**
     * 金额
     */
    @JsonProperty("FBillAmount")
    private BigDecimal fBillAmount;

    /**
     * 税额
     */
    @JsonProperty("FBillTaxAmount")
    private BigDecimal fBillTaxAmount;

    /**
     * 是否含税
     */
    @JsonProperty("FIsIncludedTax")
    private Boolean fIsIncludedTax;

    /**
     * 价目表
     */
    @JsonProperty("FPriceListId")
    private FNumber fPriceListId;

    /**
     * 折扣表
     */
    @JsonProperty("FDiscountListId")
    private FNumber fDiscountListId;

    /**
     * 结算组织客户
     */
    @JsonProperty("FSETTLECustomerID")
    private FNumber fSettleCustomerID;

    /**
     * 货主供应商
     */
    @JsonProperty("FOwnerSupplierID")
    private FNumber fOwnerSupplierID;

    /**
     * 跨组织结算生成
     */
    @JsonProperty("FISGENFORIOS")
    private Boolean fIsGenForIOS;

    /**
     * 价外税
     */
    @JsonProperty("FIsPriceExcludeTax")
    private Boolean fIsPriceExcludeTax;

    /**
     * 第三方系统来源
     */
    @JsonProperty("FThirdSrcType")
    private String fThirdSrcType;

    /**
     * 第三方单据平台单号
     */
    @JsonProperty("FThirdBillId")
    private String fThirdBillId;

    /**
     * 第三方单据编号
     */
    @JsonProperty("FThirdBillNo")
    private String fThirdBillNo;
}
