package com.ruoyi.vo.erp.salereturn;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售退货单关联关系实体
 * <AUTHOR>
 */
@Data
public class FSaleReturnEntryLink {

    /**
     * 实体主键
     */
    @JsonProperty("FLinkId")
    private Integer fLinkId;

    /**
     * 业务流程图
     */
    @JsonProperty("FEntity_Link_FFlowId")
    private FNumber fEntityLinkFFlowId;

    /**
     * 推进路线
     */
    @JsonProperty("FEntity_Link_FFlowLineId")
    private FNumber fEntityLinkFFlowLineId;

    /**
     * 转换规则
     */
    @JsonProperty("FEntity_Link_FRuleId")
    private String fEntityLinkFRuleId;

    /**
     * 源单表内码
     */
    @JsonProperty("FEntity_Link_FSTableId")
    private String fEntityLinkFSTableId;

    /**
     * 源单表
     */
    @JsonProperty("FEntity_Link_FSTableName")
    private String fEntityLinkFSTableName;

    /**
     * 源单内码
     */
    @JsonProperty("FEntity_Link_FSBillId")
    private String fEntityLinkFSBillId;

    /**
     * 源单分录内码
     */
    @JsonProperty("FEntity_Link_FSId")
    private String fEntityLinkFSId;

    /**
     * 原始携带量
     */
    @JsonProperty("FEntity_Link_FBaseunitQtyOld")
    private BigDecimal fEntityLinkFBaseUnitQtyOld;

    /**
     * 修改携带量
     */
    @JsonProperty("FEntity_Link_FBaseunitQty")
    private BigDecimal fEntityLinkFBaseUnitQty;

    /**
     * 原始携带量
     */
    @JsonProperty("FEntity_Link_FSalBaseQtyOld")
    private BigDecimal fEntityLinkFSalBaseQtyOld;

    /**
     * 修改携带量
     */
    @JsonProperty("FEntity_Link_FSalBaseQty")
    private BigDecimal fEntityLinkFSalBaseQty;

    /**
     * 原始携带量
     */
    @JsonProperty("FEntity_Link_FPriceBaseQtyOld")
    private BigDecimal fEntityLinkFPriceBaseQtyOld;

    /**
     * 修改携带量
     */
    @JsonProperty("FEntity_Link_FPriceBaseQty")
    private BigDecimal fEntityLinkFPriceBaseQty;

    /**
     * 原始携带量
     */
    @JsonProperty("FEntity_Link_FAuxUnitQtyOld")
    private BigDecimal fEntityLinkFAuxUnitQtyOld;

    /**
     * 修改携带量
     */
    @JsonProperty("FEntity_Link_FAuxUnitQty")
    private BigDecimal fEntityLinkFAuxUnitQty;

    /**
     * 迁移图
     */
    @JsonProperty("FEntity_Link_FLnkTrackerId")
    private FNumber fEntityLinkFLnkTrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FEntity_Link_FLnkSState")
    private String fEntityLinkFLnkSState;

    /**
     * 数量FLnk
     */
    @JsonProperty("FEntity_Link_FLnkAmount")
    private BigDecimal fEntityLinkFLnkAmount;

    /**
     * 迁移图
     */
    @JsonProperty("FEntity_Link_FLnk1TrackerId")
    private FNumber fEntityLinkFLnk1TrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FEntity_Link_FLnk1SState")
    private String fEntityLinkFLnk1SState;

    /**
     * 数量FLnk1
     */
    @JsonProperty("FEntity_Link_FLnk1Amount")
    private BigDecimal fEntityLinkFLnk1Amount;

    /**
     * 迁移图
     */
    @JsonProperty("FEntity_Link_FLnk2TrackerId")
    private FNumber fEntityLinkFLnk2TrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FEntity_Link_FLnk2SState")
    private String fEntityLinkFLnk2SState;

    /**
     * 数量FLnk2
     */
    @JsonProperty("FEntity_Link_FLnk2Amount")
    private BigDecimal fEntityLinkFLnk2Amount;
}
