package com.ruoyi.vo.erp.salereturn;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import com.ruoyi.vo.erp.common.FNumberBig;
import lombok.Data;

import java.util.List;

/**
 * 销售退货单上报实体
 * <AUTHOR>
 */
@Data
public class SaleReturnReport {

    /**
     * 单据编号
     */
    @JsonProperty("FBillNo")
    private String fBillNo;

    /**
     * 单据类型
     */
    @JsonProperty("FBillTypeID")
    private FNumberBig fBillTypeID;

    /**
     * 日期
     */
    @JsonProperty("FDate")
    private String fDate;

    /**
     * 销售组织
     */
    @JsonProperty("FSaleOrgId")
    private FNumber fSaleOrgId;

    /**
     * 库存组织
     */
    @JsonProperty("FStockOrgId")
    private FNumber fStockOrgId;

    /**
     * 退货客户
     */
    @JsonProperty("FRetcustId")
    private FNumber fRetcustId;

    /**
     * 库存组
     */
    @JsonProperty("FStockerGroupId")
    private FNumber fStockerGroupId;

    /**
     * 仓管员
     */
    @JsonProperty("FStockerId")
    private FNumber fStockerId;

    /**
     * 库存部门
     */
    @JsonProperty("FStockDeptId")
    private FNumber fStockDeptId;

    /**
     * 销售组
     */
    @JsonProperty("FSaleGroupId")
    private FNumber fSaleGroupId;

    /**
     * 销售部门
     */
    @JsonProperty("FSaledeptid")
    private FNumber fSaleDeptId;

    /**
     * 销售员
     */
    @JsonProperty("FSalesManId")
    private FNumber fSalesManId;

    /**
     * 结算方
     */
    @JsonProperty("FSettleCustId")
    private FNumber fSettleCustId;

    /**
     * 付款方
     */
    @JsonProperty("FPayCustId")
    private FNumber fPayCustId;

    /**
     * 收货方
     */
    @JsonProperty("FReceiveCustId")
    private FNumber fReceiveCustId;

    /**
     * 货主类型
     */
    @JsonProperty("FOwnerTypeIdHead")
    private String fOwnerTypeIdHead;

    /**
     * 货主
     */
    @JsonProperty("FOwnerIdHead")
    private FNumber fOwnerIdHead;

    /**
     * 业务类型
     */
    @JsonProperty("FBussinessType")
    private String fBusinessType;

    /**
     * 退货原因
     */
    @JsonProperty("FReturnReason")
    private String fReturnReason;

    /**
     * 收货方地址
     */
    @JsonProperty("FReceiveAddress")
    private String fReceiveAddress;

    /**
     * 交货地点
     */
    @JsonProperty("FHeadLocId")
    private FNumber fHeadLocId;

    /**
     * 跨组织业务类型
     */
    @JsonProperty("FTransferBizType")
    private FNumber fTransferBizType;

    /**
     * 对应组织
     */
    @JsonProperty("FCorrespondOrgId")
    private FNumber fCorrespondOrgId;

    /**
     * 收货方联系人
     */
    @JsonProperty("FReceiveCusContact")
    private FNumber fReceiveCusContact;

    /**
     * 序列号上传
     */
    @JsonProperty("FScanBox")
    private String fScanBox;

    /**
     * 备注
     */
    @JsonProperty("FHeadNote")
    private String fHeadNote;

    /**
     * 收货人姓名
     */
    @JsonProperty("FLinkMan")
    private String fLinkMan;

    /**
     * 联系电话
     */
    @JsonProperty("FLinkPhone")
    private String fLinkPhone;

    /**
     * 财务信息
     */
    @JsonProperty("SubHeadEntity")
    private SaleReturnReportFin subHeadEntity;

    /**
     * 明细信息
     */
    @JsonProperty("FEntity")
    private List<SaleReturnReportEntry> fEntity;
}
