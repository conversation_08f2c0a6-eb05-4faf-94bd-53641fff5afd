package com.ruoyi.vo.erp.purchasereturn;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购退料单明细上报实体
 * <AUTHOR>
 */
@Data
public class PurchaseReturnReportEntry {

    /**
     * 实体主键
     */
    @JsonProperty("FEntryID")
    private Integer fEntryID;

    /**
     * 行类型
     */
    @JsonProperty("FRowType")
    private String fRowType;

    /**
     * 物料编码
     */
    @JsonProperty("FMATERIALID")
    private FNumber fMaterialId;

    /**
     * 物料名称
     */
    @JsonProperty("FMaterialName")
    private String fMaterialName;

    /**
     * 物料说明
     */
    @JsonProperty("FMaterialDesc")
    private String fMaterialDesc;

    /**
     * 规格型号
     */
    @JsonProperty("FMateriaModel")
    private String fMaterialModel;

    /**
     * 物料类别
     */
    @JsonProperty("FMateriaType")
    private FNumber fMaterialType;

    /**
     * 库存单位
     */
    @JsonProperty("FUnitID")
    private FNumber fUnitID;

    /**
     * 基本单位
     */
    @JsonProperty("FBASEUNITID")
    private FNumber fBaseUnitID;

    /**
     * 计价单位
     */
    @JsonProperty("FPRICEUNITID")
    private FNumber fPriceUnitID;

    /**
     * 采购单位
     */
    @JsonProperty("FCarryUnitId")
    private FNumber fCarryUnitId;

    /**
     * 实退数量
     */
    @JsonProperty("FRMREALQTY")
    private BigDecimal fRmRealQty;

    /**
     * 应退数量
     */
    @JsonProperty("FRMMUSTQTY")
    private BigDecimal fRmMustQty;

    /**
     * 扣款数量
     */
    @JsonProperty("FKEAPAMTQTY")
    private BigDecimal fKeapAmtQty;

    /**
     * 补料数量
     */
    @JsonProperty("FREPLENISHQTY")
    private BigDecimal fReplenishQty;

    /**
     * 计价数量
     */
    @JsonProperty("FPRICEUNITQTY")
    private BigDecimal fPriceUnitQty;

    /**
     * 计价基本数量
     */
    @JsonProperty("FPriceBaseQty")
    private BigDecimal fPriceBaseQty;

    /**
     * 采购数量
     */
    @JsonProperty("FCarryQty")
    private BigDecimal fCarryQty;

    /**
     * 采购基本数量
     */
    @JsonProperty("FCarryBaseQty")
    private BigDecimal fCarryBaseQty;

    /**
     * 库存基本数量
     */
    @JsonProperty("FBASEUNITQTY")
    private BigDecimal fBaseUnitQty;

    /**
     * 库存辅单位数量
     */
    @JsonProperty("FAUXUNITQTY")
    private BigDecimal fAuxUnitQty;

    /**
     * 关联数量
     */
    @JsonProperty("FJOINQTY")
    private BigDecimal fJoinQty;

    /**
     * 仓库
     */
    @JsonProperty("FSTOCKID")
    private FNumber fStockId;

    /**
     * 库存状态
     */
    @JsonProperty("FStockStatusId")
    private FNumber fStockStatusId;

    /**
     * 仓位
     */
    @JsonProperty("FSTOCKLOCID")
    private FNumber fStockLocId;

    /**
     * 货主类型
     */
    @JsonProperty("FOWNERTYPEID")
    private String fOwnerTypeId;

    /**
     * 货主
     */
    @JsonProperty("FOWNERID")
    private FNumber fOwnerId;

    /**
     * 保管者类型
     */
    @JsonProperty("FKEEPERTYPEID")
    private String fKeeperTypeId;

    /**
     * 保管者
     */
    @JsonProperty("FKEEPERID")
    private FNumber fKeeperId;

    /**
     * 库存更新标识
     */
    @JsonProperty("FSTOCKFLAG")
    private Boolean fStockFlag;

    /**
     * 成本价
     */
    @JsonProperty("FCostPrice")
    private BigDecimal fCostPrice;

    /**
     * 含税单价
     */
    @JsonProperty("FTAXPRICE")
    private BigDecimal fTaxPrice;

    /**
     * 单价
     */
    @JsonProperty("FPrice")
    private BigDecimal fPrice;

    /**
     * 税率
     */
    @JsonProperty("FENTRYTAXRATE")
    private BigDecimal fEntryTaxRate;

    /**
     * 税额
     */
    @JsonProperty("FENTRYTAXAMOUNT")
    private BigDecimal fEntryTaxAmount;

    /**
     * 价税合计
     */
    @JsonProperty("FALLAMOUNT")
    private BigDecimal fAllAmount;

    /**
     * 金额
     */
    @JsonProperty("FAmount")
    private BigDecimal fAmount;

    /**
     * 总成本
     */
    @JsonProperty("FENTRYCOSTAMOUNT")
    private BigDecimal fEntryCostAmount;

    /**
     * 含税补料金额
     */
    @JsonProperty("FREPLENISHINCLTAXAMT")
    private BigDecimal fReplenishInclTaxAmt;

    /**
     * 不含税补料金额
     */
    @JsonProperty("FREPLENISHEXCLTAXAMT")
    private BigDecimal fReplenishExclTaxAmt;

    /**
     * 含税扣款金额
     */
    @JsonProperty("FKEAPINCLTAXAMT")
    private BigDecimal fKeapInclTaxAmt;

    /**
     * 不含税扣款金额
     */
    @JsonProperty("FKEAPEXCLTAXAMT")
    private BigDecimal fKeapExclTaxAmt;

    /**
     * 批号
     */
    @JsonProperty("FLot")
    private FNumber fLot;

    /**
     * 生产日期
     */
    @JsonProperty("FProduceDate")
    private String fProduceDate;

    /**
     * 到期日
     */
    @JsonProperty("FEXPIRYDATE")
    private String fExpiryDate;

    /**
     * 供应商批号
     */
    @JsonProperty("FSupplierLot")
    private String fSupplierLot;

    /**
     * 合同单号
     */
    @JsonProperty("FCONTRACTNO")
    private String fContractNo;

    /**
     * 需求跟踪号
     */
    @JsonProperty("FREQTRACENO")
    private String fReqTraceNo;

    /**
     * 订单单号
     */
    @JsonProperty("FORDERNO")
    private String fOrderNo;

    /**
     * BOM版本
     */
    @JsonProperty("FBOMID")
    private FNumber fBomId;

    /**
     * 备注
     */
    @JsonProperty("FNOTE")
    private String fNote;

    /**
     * 源单类型
     */
    @JsonProperty("FSRCBillTypeId")
    private String fSrcBillTypeId;

    /**
     * 源单编号
     */
    @JsonProperty("FSRCBillNo")
    private String fSrcBillNo;

    /**
     * 源单行号
     */
    @JsonProperty("FSRCSeq")
    private String fSrcSeq;

    /**
     * 项目编号
     */
    @JsonProperty("FProjectNo")
    private String fProjectNo;

    /**
     * 项目
     */
    @JsonProperty("F_PCZO_Base")
    private FNumber fPczoBase;

    /**
     * 项目经理
     */
    @JsonProperty("F_PCZO_Base1")
    private FNumber fPczoBase1;

    /**
     * 关联关系表
     */
    @JsonProperty("FPURMRBENTRY_Link")
    private List<FPurMrbEntryLink> fPurMrbEntryLink;
}
