package com.ruoyi.vo.erp.purchasestock;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购入库单明细上报实体
 * <AUTHOR>
 */
@Data
public class PurchaseInStockReportEntry {

    /**
     * 实体主键
     */
    @JsonProperty("FEntryID")
    private Integer fEntryID;

    /**
     * 行类型
     */
    @JsonProperty("FRowType")
    private String fRowType;

    /**
     * 物料编码
     */
    @JsonProperty("FMaterialId")
    private FNumber fMaterialId;

    /**
     * 库存单位
     */
    @JsonProperty("FUnitID")
    private FNumber fUnitID;

    /**
     * 基本单位
     */
    @JsonProperty("FBaseUnitID")
    private FNumber fBaseUnitID;

    /**
     * 计价单位
     */
    @JsonProperty("FPriceUnitID")
    private FNumber fPriceUnitID;

    /**
     * 采购单位
     */
    @JsonProperty("FRemainInStockUnitId")
    private FNumber fRemainInStockUnitId;

    /**
     * 物料说明
     */
    @JsonProperty("FMaterialDesc")
    private String fMaterialDesc;

    /**
     * 实收数量
     */
    @JsonProperty("FRealQty")
    private BigDecimal fRealQty;

    /**
     * 采购数量
     */
    @JsonProperty("FRemainInStockQty")
    private BigDecimal fRemainInStockQty;

    /**
     * 采购基本数量
     */
    @JsonProperty("FRemainInStockBaseQty")
    private BigDecimal fRemainInStockBaseQty;

    /**
     * 计价基本数量
     */
    @JsonProperty("FPriceBaseQty")
    private BigDecimal fPriceBaseQty;

    /**
     * 库存基本数量
     */
    @JsonProperty("FBaseUnitQty")
    private BigDecimal fBaseUnitQty;

    /**
     * 未关联应付数量（计价单位）
     */
    @JsonProperty("FAPNotJoinQty")
    private BigDecimal fAPNotJoinQty;

    /**
     * 仓库
     */
    @JsonProperty("FStockId")
    private FNumber fStockId;

    /**
     * 库存状态
     */
    @JsonProperty("FStockStatusId")
    private FNumber fStockStatusId;

    /**
     * 货主类型
     */
    @JsonProperty("FOWNERTYPEID")
    private String fOwnerTypeId;

    /**
     * 货主
     */
    @JsonProperty("FOWNERID")
    private FNumber fOwnerId;

    /**
     * 税率(%)
     */
    @JsonProperty("FEntryTaxRate")
    private BigDecimal fEntryTaxRate;

    /**
     * 仓位
     */
    @JsonProperty("FStockLocId")
    private FStockLocId fStockLocId;

    /**
     * 批号
     */
    @JsonProperty("FLot")
    private FNumber fLot;

    /**
     * 生产日期
     */
    @JsonProperty("FProduceDate")
    private String fProduceDate;

    /**
     * 有效期至
     */
    @JsonProperty("FExpiryDate")
    private String fExpiryDate;

    /**
     * 备注
     */
    @JsonProperty("FNote")
    private String fNote;

    /**
     * 供应商批号
     */
    @JsonProperty("FSupplierLot")
    private String fSupplierLot;

    /**
     * 毛重
     */
    @JsonProperty("FGrossWeight")
    private BigDecimal fGrossWeight;

    /**
     * 净重
     */
    @JsonProperty("FNetWeight")
    private BigDecimal fNetWeight;

    /**
     * 物料名称
     */
    @JsonProperty("FMaterialName")
    private String fMaterialName;

    /**
     * 物料类别
     */
    @JsonProperty("FMaterialType")
    private FNumber fMaterialType;

    /**
     * 规格型号
     */
    @JsonProperty("FUOM")
    private String fUOM;

    /**
     * 合同单号
     */
    @JsonProperty("FContractlNo")
    private String fContractlNo;

    /**
     * 需求跟踪号
     */
    @JsonProperty("FReqTraceNo")
    private String fReqTraceNo;

    /**
     * 应收数量
     */
    @JsonProperty("FMustQty")
    private BigDecimal fMustQty;

    /**
     * 数量（库存辅单位）
     */
    @JsonProperty("FAuxUnitQty")
    private BigDecimal fAuxUnitQty;

    /**
     * 源单行内码
     */
    @JsonProperty("FSRCRowId")
    private String fSRCRowId;

    /**
     * 免费
     */
    @JsonProperty("FIsFree")
    private Boolean fIsFree;

    /**
     * BOM版本
     */
    @JsonProperty("FBOMId")
    private FNumber fBOMId;

    /**
     * 含税单价
     */
    @JsonProperty("FTaxPrice")
    private BigDecimal fTaxPrice;

    /**
     * 成本价
     */
    @JsonProperty("FCostPrice")
    private BigDecimal fCostPrice;

    /**
     * 税额
     */
    @JsonProperty("FEntryTaxAmount")
    private BigDecimal fEntryTaxAmount;

    /**
     * 折扣率(%)
     */
    @JsonProperty("FDiscountRate")
    private BigDecimal fDiscountRate;

    /**
     * 价格系数
     */
    @JsonProperty("FPriceCoefficient")
    private BigDecimal fPriceCoefficient;

    /**
     * 计价数量
     */
    @JsonProperty("FPriceUnitQty")
    private BigDecimal fPriceUnitQty;

    /**
     * 净价
     */
    @JsonProperty("FTaxNetPrice")
    private BigDecimal fTaxNetPrice;

    /**
     * 总成本
     */
    @JsonProperty("FEntryCostAmount")
    private BigDecimal fEntryCostAmount;

    /**
     * 价税合计
     */
    @JsonProperty("FAllAmount")
    private BigDecimal fAllAmount;

    /**
     * 税额(本位币)
     */
    @JsonProperty("FTaxAmount_LC")
    private BigDecimal fTaxAmountLC;

    /**
     * 总成本(本位币)
     */
    @JsonProperty("FCostAmount_LC")
    private BigDecimal fCostAmountLC;

    /**
     * 价税合计(本位币)
     */
    @JsonProperty("FAllAmount_LC")
    private BigDecimal fAllAmountLC;

    /**
     * 入库库存更新标志
     */
    @JsonProperty("FStockFlag")
    private Boolean fStockFlag;

    /**
     * 基本单位单价
     */
    @JsonProperty("FBaseUnitPrice")
    private BigDecimal fBaseUnitPrice;

    /**
     * 库存辅单位
     */
    @JsonProperty("FAuxUnitID")
    private FNumber fAuxUnitID;

    /**
     * 关联数量(基本单位)
     */
    @JsonProperty("FBaseJoinQty")
    private BigDecimal fBaseJoinQty;

    /**
     * 辅助属性
     */
    @JsonProperty("FAuxPropId")
    private FNumber fAuxPropId;

    /**
     * 订单单号
     */
    @JsonProperty("FPOOrderNo")
    private String fPOOrderNo;

    /**
     * 收货库存状态
     */
    @JsonProperty("FReceiveStockStatus")
    private FNumber fReceiveStockStatus;

    /**
     * 保质期单位
     */
    @JsonProperty("FEXPUnit")
    private FNumber fEXPUnit;

    /**
     * 保质期
     */
    @JsonProperty("FExpPeriod")
    private Integer fExpPeriod;

    /**
     * 源单类型
     */
    @JsonProperty("FSRCBILLTYPEID")
    private String fSrcBillTypeId;

    /**
     * 源单编号
     */
    @JsonProperty("FSRCBillNo")
    private String fSrcBillNo;

    /**
     * 保管者类型
     */
    @JsonProperty("FKeeperTypeId")
    private String fKeeperTypeId;

    /**
     * 保管者
     */
    @JsonProperty("FKeeperID")
    private FNumber fKeeperID;

    /**
     * 关联关系表
     */
    @JsonProperty("FInStockEntry_Link")
    private List<FInStockEntryLink> fInStockEntryLink;

    /**
     * 仓位信息
     */
    @Data
    public static class FStockLocId {
        /**
         * 区域
         */
        @JsonProperty("FF100012")
        private FNumber fF100012;

        /**
         * 位置
         */
        @JsonProperty("FF100013")
        private FNumber fF100013;

        /**
         * 楼层
         */
        @JsonProperty("FF100008")
        private FNumber fF100008;

        /**
         * 货架
         */
        @JsonProperty("FF100009")
        private FNumber fF100009;

        /**
         * 货位号
         */
        @JsonProperty("FF100010")
        private FNumber fF100010;
    }
}
